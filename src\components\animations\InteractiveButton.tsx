import { Button, ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import React, {
  forwardRef,
  ReactNode,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

interface InteractiveButtonProps extends ButtonProps {
  children: ReactNode;
  ripple?: boolean;
  lift?: boolean;
  glow?: boolean;
  glowColor?: string;
}

export const InteractiveButton = forwardRef<
  HTMLButtonElement,
  InteractiveButtonProps
>(
  (
    {
      children,
      className,
      variant,
      size,
      ripple = true,
      lift = true,
      glow = true,
      glowColor = "hsl(var(--primary) / 0.4)",
      onClick,
      ...props
    },
    ref
  ) => {
    const [ripples, setRipples] = useState<
      Array<{ x: number; y: number; id: number }>
    >([]);
    const localRef = useRef<HTMLButtonElement>(null);

    useImperativeHandle(ref, () => localRef.current as HTMLButtonElement);
    const handleClick = useCallback(
      (e: React.MouseEvent<HTMLButtonElement>) => {
        if (ripple && localRef.current) {
          const rect = localRef.current.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;
          const id = Date.now();
          setRipples((prev) => [...prev, { x, y, id }]);
          setTimeout(
            () => setRipples((prev) => prev.filter((r) => r.id !== id)),
            600
          );
        }
        onClick?.(e);
      },
      [ripple, onClick]
    );

    return (
      <Button
        variant={variant}
        size={size}
        className={cn(
          "relative overflow-hidden rounded-full font-semibold",
          className
        )}
        {...props}
        asChild
      >
        <motion.button
          ref={localRef}
          onClick={handleClick}
          whileHover={{
            scale: lift ? 1.05 : 1,
            boxShadow: glow ? `0 0 25px ${glowColor}` : undefined,
          }}
          whileTap={{ scale: lift ? 0.95 : 1 }}
          transition={{ type: "spring", stiffness: 300, damping: 20 }}
        >
          <AnimatePresence>
            {ripples.map((r) => (
              <motion.span
                key={r.id}
                className="pointer-events-none absolute rounded-full bg-white/30"
                style={{ left: r.x, top: r.y, x: "-50%", y: "-50%" }}
                initial={{ width: 0, height: 0, opacity: 0.5 }}
                animate={{ width: "200%", paddingTop: "200%", opacity: 0 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
              />
            ))}
          </AnimatePresence>
          <span className="relative z-10 flex items-center justify-center px-3">
            {children}
          </span>
        </motion.button>
      </Button>
    );
  }
);

InteractiveButton.displayName = "InteractiveButton";
