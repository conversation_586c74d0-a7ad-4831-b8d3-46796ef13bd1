import { AnimatePresence, motion } from "framer-motion";
import { lazy, useEffect } from "react";
import { BrowserRouter, Route, Routes, useLocation } from "react-router-dom";
import ErrorBoundary from "./components/common/ErrorBoundary";
import PilotDialog from "./components/common/PilotDialog";
import { pageVariants, smoothTransition } from "./lib/animations";

const Index = lazy(() => import("@/pages/Index"));
const NotFound = lazy(() => import("@/pages/NotFound"));

const AnimatedRoute = ({ children }: { children: React.ReactNode }) => (
  <motion.div
    variants={pageVariants}
    initial="initial"
    animate="animate"
    exit="exit"
    transition={smoothTransition}
  >
    {children}
  </motion.div>
);

const AnimatedRoutes = () => {
  const location = useLocation();
  return (
    <AnimatePresence mode="wait">
      <Routes location={location} key={location.pathname}>
        <Route
          path="/"
          element={
            <AnimatedRoute>
              <Index />
            </AnimatedRoute>
          }
        />
        <Route
          path="*"
          element={
            <AnimatedRoute>
              <NotFound />
            </AnimatedRoute>
          }
        />
      </Routes>
    </AnimatePresence>
  );
};

const App = () => {
  useEffect(() => {
    const preloadImages = [
      "/logo.png",
      "/aura/aura-bg-desktop.png",
      "/hero/hero-bg-desktop.png",
      "scrollytelling/security-shield.png",
      "scrollytelling/frame-a-rivers.png",
      "scrollytelling/frame-b-mycelium.png",
      "scrollytelling/frame-c-fireflies.png",
      "scrollytelling/frame-d-emblem.png",
    ];
    preloadImages.forEach((src) => {
      const img = new Image();
      img.src = src;
    });
  }, []);

  return (
    <ErrorBoundary>
      <PilotDialog />
      <BrowserRouter>
        <main id="main-content">
          <AnimatedRoutes />
        </main>
      </BrowserRouter>
    </ErrorBoundary>
  );
};

export default App;
