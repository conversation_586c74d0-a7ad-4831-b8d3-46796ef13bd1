import { InteractiveButton } from "@/components/animations";
import { motion } from "framer-motion";
import { AlertTriangle } from "lucide-react";
import React, { memo } from "react";
import { Link } from "react-router-dom";

const NotFound: React.FC = memo(() => {
  return (
    <motion.div
      className="flex min-h-screen flex-col items-center justify-center bg-slate-50 p-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        className="relative w-full max-w-lg rounded-2xl border border-slate-200 bg-white p-10 shadow-xl"
        initial={{ y: 30, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <motion.div
          className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-red-50"
          animate={{ rotate: [0, 10, -10, 0] }}
          transition={{ repeat: Infinity, duration: 2 }}
        >
          <AlertTriangle className="h-8 w-8 text-red-600" />
        </motion.div>

        <h1 className="text-center text-4xl font-extrabold text-slate-900">
          404 — Page Not Found
        </h1>

        <p className="mt-3 text-center text-lg text-slate-500">
          Oops! The page you are looking for doesn’t exist or has been moved.
        </p>

        <div className="mt-8 flex justify-center gap-4">
          <InteractiveButton asChild variant="default">
            <Link to="/">Go Home</Link>
          </InteractiveButton>
          <InteractiveButton asChild variant="outline">
            <Link to="/">Contact Support</Link>
          </InteractiveButton>
        </div>
      </motion.div>
    </motion.div>
  );
});

NotFound.displayName = "NotFound";

export default NotFound;
