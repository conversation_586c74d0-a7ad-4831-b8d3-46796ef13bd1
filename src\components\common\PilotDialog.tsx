import { InteractiveButton } from "@/components/animations";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import React, { useCallback, useEffect, useState } from "react";

const PilotDialog: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [fullName, setFullName] = useState("");
  const [role, setRole] = useState<"physician" | "administration">("physician");
  const [hospital, setHospital] = useState("");
  const [city, setCity] = useState("");
  const [phone, setPhone] = useState("");
  const [notes, setNotes] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const handler = () => setOpen(true);
    window.addEventListener("early-pilot-open", handler as EventListener);
    return () =>
      window.removeEventListener("early-pilot-open", handler as EventListener);
  }, []);

  const resetForm = useCallback(() => {
    setEmail("");
    setFullName("");
    setHospital("");
    setCity("");
    setPhone("");
    setNotes("");
    setRole("physician");
    setSubmitted(false);
    setError("");
  }, []);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      if (!fullName.trim() || !email.trim() || !hospital.trim()) return;
      setIsSubmitting(true);
      setError("");
      try {
        const payload = {
          source: "pilot",
          email: email.trim(),
          name: fullName.trim(),
          role,
          hospital: hospital.trim(),
          city: city.trim(),
          phone: phone.trim(),
          notes: notes.trim(),
        };
        const res = await fetch(
          `${import.meta.env.VITE_NODE_API_URL}/api/lead`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          }
        );

        const data = await res.json();
        if (!res.ok) {
          throw new Error(data.error || "Failed to submit form");
        }
        setSubmitted(true);
      } catch (error: any) {
        setError(error.message || "Failed to submit. Please try again.");
      } finally {
        setIsSubmitting(false);
      }
    },
    [email, fullName, role, hospital, city, phone, notes]
  );

  return (
    <Dialog
      open={open}
      onOpenChange={(v) => {
        setOpen(v);
        if (!v) resetForm();
      }}
    >
      <DialogContent className="w-full max-w-3xl overflow-hidden p-4">
        {!submitted ? (
          <>
            <DialogHeader className="space-y-2">
              <DialogTitle className="text-2xl font-semibold">
                Partner for an Early Pilot
              </DialogTitle>
              <DialogDescription>
                Book a personalized walkthrough to explore a limited early pilot
                with your oncology team.
              </DialogDescription>
            </DialogHeader>

            <form className="mt-3 grid gap-2" onSubmit={handleSubmit}>
              <div className="grid gap-3">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <Input
                    id="pilot-name"
                    type="text"
                    required
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    placeholder="Full name"
                  />
                  <Input
                    id="pilot-email"
                    type="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Company email"
                  />
                </div>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <Input
                    id="pilot-hospital"
                    type="text"
                    required
                    value={hospital}
                    onChange={(e) => setHospital(e.target.value)}
                    placeholder="Hospital / Institution"
                  />

                  <Input
                    id="pilot-city"
                    type="text"
                    value={city}
                    onChange={(e) => setCity(e.target.value)}
                    placeholder="City"
                  />
                </div>
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <span className="text-sm font-medium">Your role</span>
                    <div className="flex gap-2">
                      <InteractiveButton
                        type="button"
                        variant={role === "physician" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setRole("physician")}
                      >
                        Physician
                      </InteractiveButton>
                      <InteractiveButton
                        type="button"
                        variant={
                          role === "administration" ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => setRole("administration")}
                      >
                        Administration
                      </InteractiveButton>
                    </div>
                  </div>
                  <Input
                    id="pilot-phone"
                    type="text"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="Phone"
                  />
                </div>
                <textarea
                  id="pilot-notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Key context (e.g., tumor board workflow, EHR vendors)"
                  className="min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                />
                {error && (
                  <div className="rounded-md bg-red-50 p-3 text-sm text-red-600">
                    {error}
                  </div>
                )}
                <div className="text-center">
                  <InteractiveButton
                    variant="primary"
                    type="submit"
                    size="lg"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Submitting..." : "Book Early Pilot"}
                  </InteractiveButton>
                </div>
              </div>
              <p className="mt-2 text-xs text-muted-foreground">
                By submitting, you agree to be contacted about a pilot. No spam.
                We respect your privacy.
              </p>
            </form>
          </>
        ) : (
          <div className="py-12 text-center">
            <h3 className="text-2xl font-semibold text-foreground">
              Thank you!
            </h3>
            <p className="mt-2 text-muted-foreground">
              We've received your request and will be in touch shortly.
            </p>
            <div className="mt-3">
              <InteractiveButton
                variant="primary"
                type="submit"
                size="lg"
                onClick={() => {
                  setOpen(false);
                  resetForm();
                }}
              >
                Close
              </InteractiveButton>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default PilotDialog;
