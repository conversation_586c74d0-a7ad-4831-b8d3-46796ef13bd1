import { motion } from "framer-motion";
import { useState } from "react";

const SaferActionsView = () => {
  const [viewMode, setViewMode] = useState<"summary" | "full">(() => {
    if (typeof window === "undefined") return "summary";
    return (
      (localStorage.getItem("safer-actions-view-mode") as "summary" | "full") ||
      "summary"
    );
  });

  const [safetyIssuesCaught, setSafetyIssuesCaught] = useState({
    today: 2,
    week: 8,
  });
  const [tasksScheduled, setTasksScheduled] = useState({ week: 4 });

  const [regimenStatus, setRegimenStatus] = useState<
    "draft" | "accepted" | "edited"
  >("draft");
  const [showDrugExplainer, setShowDrugExplainer] = useState<string | null>(
    null
  );
  const [safetySignals, setSafetySignals] = useState({
    ddi: { active: true, severity: "moderate" as "low" | "moderate" | "high" },
    allergy: { active: true, severity: "low" as "low" | "moderate" | "high" },
    completeness: { active: true, missing: ["CBC", "LFT"] },
  });

  const [taskFilter, setTaskFilter] = useState<"mine" | "team" | "all">("mine");
  const [tasks, setTasks] = useState([
    {
      id: "1",
      title: "CBC (with diff)",
      due: "Tue",
      status: "scheduled" as const,
      assignee: "RN",
    },
    {
      id: "2",
      title: "Refill Tamoxifen",
      due: "30 days",
      status: "done" as const,
      assignee: "Pharmacy",
    },
    {
      id: "3",
      title: "Follow-up",
      due: "3 months",
      status: "overdue" as const,
      assignee: "MD",
    },
  ]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.4, ease: [0.22, 1, 0.36, 1] }}
      className="flex h-full flex-col items-center justify-center gap-6 px-4 pb-4 pt-20 md:flex-row"
    >
      <div className="flex w-full max-w-md flex-col items-start text-left md:order-first md:w-1/4">
        <div className="inline-flex items-center gap-2 rounded-[20px] border border-white/70 bg-white/90 px-5 py-2.5 shadow-[0_8px_20px_rgba(0,0,0,0.1)] backdrop-blur-2xl">
          <div
            className="h-1.5 w-1.5 rounded-full"
            style={{ backgroundColor: "#ec4899" }}
          />
          <div className="flex items-baseline gap-1">
            <span className="text-[10px] font-bold tracking-wider text-slate-800">
              SAFER ACTIONS
            </span>
            <span className="text-[8px] font-medium tracking-wide text-slate-600">
              & FOLLOW-UPS
            </span>
          </div>
        </div>

        <h2 className="mt-6 text-6xl font-light leading-[1.05] text-white">
          <span className="font-semibold">Safer Actions,</span>
          <br />
          <span className="italic">& Follow-ups</span>
        </h2>
        <div className="mt-6 max-w-md">
          <p className="text-[14px] font-light text-white/90">
            Propose, check, and orchestrate complex care plans—all from one
            screen with built-in safety checks.
          </p>
          <div
            role="list"
            aria-label="Capabilities"
            className="mt-2 flex flex-wrap gap-2"
          >
            {["DDI/allergy alerts", "Trial matches", "Status tracker"].map(
              (cap) => (
                <span
                  key={cap}
                  role="listitem"
                  className="inline-flex items-center gap-1 rounded-full border border-white/60 bg-white/90 px-3 py-1 text-[10px] font-medium text-slate-800 backdrop-blur-md focus:outline-none focus-visible:ring-2 focus-visible:ring-white/80"
                  aria-label={cap}
                >
                  {cap}
                </span>
              )
            )}
          </div>
        </div>
      </div>

      <div className="relative w-full flex-1 md:order-last md:w-2/3">
        <div className="relative min-h-0 w-full flex-1 overflow-hidden rounded-xl bg-white/95 shadow-2xl ring-1 ring-slate-200/60">
          <div className="border-b border-slate-200 bg-white/80 px-4 py-2 backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2">
                  <img src="/logo.png" alt="Entheory" className="h-6 w-auto" />
                </div>
                <nav className="hidden items-center gap-6 sm:flex">
                  <button className="text-[10px] text-gray-600 hover:text-gray-900">
                    Records
                  </button>
                  <button className="text-[10px] text-gray-600 hover:text-gray-900">
                    Timeline
                  </button>
                  <button className="text-[10px] text-gray-600 hover:text-gray-900">
                    Summary
                  </button>
                  <button className="text-[10px] font-medium text-pink-600">
                    Actions
                  </button>
                </nav>
              </div>
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-4 text-[8px]">
                  <div className="flex items-center gap-2">
                    <div className="flex h-5 w-5 items-center justify-center rounded-full bg-pink-100">
                      <svg
                        className="h-3 w-3 text-pink-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth={2}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-700">
                      Safety issues caught:{" "}
                      <span className="font-semibold text-pink-600">
                        {safetyIssuesCaught.today}
                      </span>{" "}
                      (today)
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex h-5 w-5 items-center justify-center rounded-full bg-emerald-100">
                      <svg
                        className="h-3 w-3 text-emerald-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth={2}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                    <span className="text-gray-700">
                      Auto‑scheduled:{" "}
                      <span className="font-semibold text-emerald-600">
                        {tasksScheduled.week}
                      </span>{" "}
                      (this week)
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="border-b border-gray-200 bg-white px-3 py-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-12">
                <div className="flex items-center gap-2">
                  <span className="text-[10px] text-gray-500">Patient:</span>
                  <span className="text-[10px] font-medium text-gray-900">
                    Priya Sharma
                  </span>
                  <span className="rounded bg-pink-100 px-2 py-0.5 text-[8px] font-medium text-pink-700">
                    Active Plan
                  </span>
                  <span className="text-[8px] text-gray-500">
                    MRN: PS-2025-847
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <button className="flex items-center gap-2 text-[8px] text-gray-600 hover:text-gray-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500">
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                    All Actions
                  </button>
                  <button className="flex items-center gap-2 text-[8px] text-gray-600 hover:text-gray-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500">
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                    Safety Checks
                  </button>

                  <div
                    className="ml-2 inline-flex overflow-hidden rounded-full border border-gray-300"
                    role="group"
                    aria-label="View mode"
                  >
                    <button
                      className={`px-3 py-1 text-[8px] font-medium ${viewMode === "summary" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"} focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500`}
                      onClick={() => {
                        setViewMode("summary");
                      }}
                      aria-pressed={viewMode === "summary"}
                    >
                      Summary
                    </button>
                    <button
                      className={`px-3 py-1 text-[8px] font-medium ${viewMode === "full" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"} focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500`}
                      onClick={() => {
                        setViewMode("full");
                      }}
                      aria-pressed={viewMode === "full"}
                    >
                      Full
                    </button>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="hidden items-center gap-2 text-[8px] text-gray-600 md:flex">
                  <span className="rounded bg-gray-100 px-2 py-0.5">
                    Protocol v2.1
                  </span>
                  <button className="underline hover:text-gray-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500">
                    View safety log
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="flex h-full" style={{ maxHeight: "70vh" }}>
            <div className="scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100 w-2/3 space-y-2 overflow-y-auto">
              <motion.div
                className="rounded-xl bg-white shadow-sm ring-1 ring-slate-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <div className="border-b border-slate-200 p-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-[12px] font-semibold text-slate-900">
                      Suggested regimen
                    </h3>
                    <span
                      className={`rounded-full px-2.5 py-1 text-[8px] font-medium ${
                        regimenStatus === "accepted"
                          ? "bg-emerald-100 text-emerald-700"
                          : regimenStatus === "edited"
                            ? "bg-amber-100 text-amber-700"
                            : "bg-gray-100 text-gray-700"
                      }`}
                    >
                      {regimenStatus === "accepted"
                        ? "Accepted"
                        : regimenStatus === "edited"
                          ? "Modified"
                          : "Draft"}
                    </span>
                  </div>
                </div>

                <div className="px-3 py-2">
                  <div
                    className={`rounded-lg border p-2 ${
                      viewMode === "full"
                        ? "border-slate-200 bg-slate-50"
                        : "border-transparent"
                    }`}
                  >
                    <div className="space-y-1">
                      <div className="flex items-start justify-center gap-2">
                        <div className="flex-1">
                          <span className="text-[10px] text-slate-800">
                            Palbociclib 125 mg PO daily, days 1–21 of 28-day
                            cycle
                          </span>
                          <button
                            className="ml-2 inline-flex h-5 w-5 items-center justify-center rounded-full transition-colors hover:bg-slate-200"
                            onClick={() => {
                              setShowDrugExplainer(
                                showDrugExplainer === "palbociclib"
                                  ? null
                                  : "palbociclib"
                              );
                            }}
                            aria-label="Why palbociclib?"
                            aria-expanded={showDrugExplainer === "palbociclib"}
                          >
                            <span className="text-[10px] text-slate-500">
                              ⋯
                            </span>
                          </button>

                          {showDrugExplainer === "palbociclib" && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: "auto" }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.2 }}
                              className="mt-2 overflow-hidden"
                            >
                              <div className="rounded-md border border-pink-200 bg-pink-50 p-2">
                                <p className="mb-1 text-[8px] font-medium text-pink-900">
                                  Why recommended:
                                </p>
                                <p className="text-[8px] text-pink-800">
                                  CDK4/6 inhibitor indicated for ER+/PR+, HER2–
                                  breast cancer. NCCN/ESMO class IIa evidence in
                                  this context.
                                </p>
                                <div className="mt-2 flex items-center gap-2">
                                  <span className="text-[11px] text-pink-700">
                                    Citations:
                                  </span>
                                  <button className="rounded-full border border-pink-300 bg-white px-2 py-0.5 text-[11px] text-pink-700 hover:bg-pink-100">
                                    MONARCH-3
                                  </button>
                                  <button className="rounded-full border border-pink-300 bg-white px-2 py-0.5 text-[11px] text-pink-700 hover:bg-pink-100">
                                    PALOMA-2
                                  </button>
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-start justify-center gap-2">
                        <div className="flex-1">
                          <span className="text-[10px] text-slate-800">
                            Letrozole 2.5 mg PO daily
                          </span>
                          <button
                            className="ml-2 inline-flex h-5 w-5 items-center justify-center rounded-full transition-colors hover:bg-slate-200"
                            onClick={() => {
                              setShowDrugExplainer(
                                showDrugExplainer === "letrozole"
                                  ? null
                                  : "letrozole"
                              );
                            }}
                            aria-label="Why letrozole?"
                            aria-expanded={showDrugExplainer === "letrozole"}
                          >
                            <span className="text-[10px] text-slate-500">
                              ⋯
                            </span>
                          </button>

                          {showDrugExplainer === "letrozole" && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: "auto" }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.2 }}
                              className="mt-2 overflow-hidden"
                            >
                              <div className="rounded-md border border-pink-200 bg-pink-50 p-2">
                                <p className="mb-1 text-[8px] font-medium text-pink-900">
                                  Why recommended:
                                </p>
                                <p className="text-[8px] text-pink-800">
                                  Aromatase inhibitor for postmenopausal ER+
                                  breast cancer. Prior response to endocrine
                                  therapy noted.
                                </p>
                                <div className="mt-2 flex items-center gap-2">
                                  <span className="text-[11px] text-pink-700">
                                    Citations:
                                  </span>
                                  <button className="rounded-full border border-pink-300 bg-white px-2 py-0.5 text-[11px] text-pink-700 hover:bg-pink-100">
                                    BIG 1-98
                                  </button>
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-1 flex items-center gap-4">
                    <button
                      className={`rounded-lg p-1 text-[10px] font-medium transition-all ${
                        safetySignals.ddi.active || safetySignals.allergy.active
                          ? "cursor-not-allowed bg-gray-100 text-gray-400"
                          : "bg-pink-600 text-white hover:bg-pink-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                      }`}
                      disabled={
                        safetySignals.ddi.active || safetySignals.allergy.active
                      }
                      onClick={() => {
                        if (
                          !safetySignals.ddi.active &&
                          !safetySignals.allergy.active
                        ) {
                          setRegimenStatus("accepted");
                        }
                      }}
                    >
                      Accept
                    </button>
                    <button
                      className="rounded-lg border border-slate-300 p-1 text-[10px] font-medium text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500"
                      onClick={() => {
                        setRegimenStatus("edited");
                      }}
                    >
                      Edit
                    </button>
                    <button className="rounded-lg border border-slate-300 p-1 text-[10px] font-medium text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500">
                      Defer
                    </button>
                  </div>

                  <div className="mt-1 border-t border-slate-200 pt-2">
                    <p className="text-[8px] text-slate-600">
                      <span className="font-medium">Derived from:</span>{" "}
                      <button className="underline hover:text-slate-900">
                        ER+/PR+, HER2– (Obs: 2025-08-20)
                      </button>
                      {", "}
                      <button className="underline hover:text-slate-900">
                        prior 1L AC+T (RX: 2025-04-02)
                      </button>
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="rounded-xl bg-white p-2 shadow-sm ring-1 ring-slate-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.35 }}
              >
                <h3 className="mb-2 text-[12px] font-semibold text-slate-900">
                  Safety Signals
                </h3>

                <div className="space-y-3">
                  <motion.div
                    className={`flex items-start gap-2 rounded-lg border p-2 transition-all ${
                      safetySignals.ddi.active
                        ? "border-amber-300 bg-amber-50 hover:bg-amber-100"
                        : "border-gray-200 bg-gray-50 opacity-60"
                    }`}
                    animate={
                      safetySignals.ddi.active ? { scale: [1, 1.02, 1] } : {}
                    }
                    transition={{ duration: 0.3 }}
                  >
                    <div className="mt-0.5">
                      <svg
                        className="h-5 w-5 text-amber-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="rounded-full bg-amber-200 px-2 py-0.5 text-[8px] font-medium text-amber-800">
                          DDI
                        </span>
                        <span className="text-[10px] font-medium text-amber-900">
                          Warfarin interaction
                        </span>
                        <span className="text-[8px] text-amber-700">
                          (↑INR risk)
                        </span>
                      </div>
                      <p className="mt-1 text-[8px] text-amber-800">
                        CDK4/6 inhibitor may increase INR via CYP3A4 interaction
                      </p>
                      {viewMode === "full" && (
                        <div className="mt-1 flex items-center gap-2">
                          <button className="rounded-md border border-amber-300 bg-white px-2 py-1 text-[8px] text-amber-700 hover:bg-amber-50">
                            Switch to DOAC
                          </button>
                          <button className="rounded-md border border-amber-300 bg-white px-2 py-1 text-[8px] text-amber-700 hover:bg-amber-50">
                            ↑ INR monitoring
                          </button>
                        </div>
                      )}
                    </div>
                    <button className="text-[8px] text-amber-700 hover:text-amber-900">
                      Details →
                    </button>
                  </motion.div>

                  <motion.div
                    className={`flex items-start gap-2 rounded-lg border p-2 transition-all ${
                      safetySignals.allergy.active
                        ? "border-red-300 bg-red-50 hover:bg-red-100"
                        : "border-gray-200 bg-gray-50 opacity-60"
                    }`}
                    animate={
                      safetySignals.allergy.active
                        ? { scale: [1, 1.02, 1] }
                        : {}
                    }
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <div className="mt-0.5">
                      <svg
                        className="h-5 w-5 text-red-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="rounded-full bg-red-200 px-2 py-0.5 text-[8px] font-medium text-red-800">
                          Allergy
                        </span>
                        <span className="text-[10px] font-medium text-red-900">
                          Taxane allergy on file
                        </span>
                      </div>
                      <p className="mt-1 text-[8px] text-red-800">
                        Historical rash (grade 2) on docetaxel 2023-06-10
                      </p>
                      {viewMode === "full" && (
                        <div className="mt-2 text-[8px] text-red-700">
                          Note: Current regimen does not contain taxanes
                        </div>
                      )}
                    </div>
                    <button className="text-[8px] text-red-700 hover:text-red-900">
                      Details →
                    </button>
                  </motion.div>

                  <motion.div
                    className={`flex items-start gap-2 rounded-lg border p-2 transition-all ${
                      safetySignals.completeness.active
                        ? "border-amber-300 bg-amber-50 hover:bg-amber-100"
                        : "border-gray-200 bg-gray-50 opacity-60"
                    }`}
                    animate={
                      safetySignals.completeness.active
                        ? { scale: [1, 1.02, 1] }
                        : {}
                    }
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <div className="mt-0.5">
                      <svg
                        className="h-5 w-5 text-amber-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="rounded-full bg-amber-200 px-2 py-0.5 text-[8px] font-medium text-amber-800">
                          Completeness
                        </span>
                        <span className="text-[10px] font-medium text-amber-900">
                          Baseline labs needed
                        </span>
                      </div>
                      <p className="mt-1 text-[8px] text-amber-800">
                        {safetySignals.completeness.missing.join(", ")} required
                        within 7 days
                      </p>
                      <div className="mt-2 flex items-center gap-2">
                        {safetySignals.completeness.missing.map((lab) => (
                          <button
                            key={lab}
                            className="rounded-md border border-amber-300 bg-white px-2 py-1 text-[8px] text-amber-700 hover:bg-amber-50"
                            onClick={() => {
                              setSafetySignals((prev) => ({
                                ...prev,
                                completeness: {
                                  ...prev.completeness,
                                  missing: prev.completeness.missing.filter(
                                    (m) => m !== lab
                                  ),
                                },
                              }));
                            }}
                          >
                            Order {lab}
                          </button>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                </div>

                {viewMode === "full" && (
                  <div className="mt-1 border-t border-slate-200 pt-2">
                    <p className="text-[8px] text-slate-600">
                      <span className="font-medium">Safety check engine:</span>{" "}
                      v2.1.0 • Last updated 2 min ago •{" "}
                      <button className="underline hover:text-slate-900">
                        View full log
                      </button>
                    </p>
                  </div>
                )}
              </motion.div>

              <motion.div
                className="rounded-xl bg-white shadow-sm ring-1 ring-slate-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <div className="border-b border-slate-200 p-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-[12px] font-semibold text-slate-900">
                      Clinical Trial Match
                    </h3>
                    <span className="rounded-full bg-purple-100 p-1 text-[8px] font-medium text-purple-700">
                      Phase II • ER+ cohort
                    </span>
                  </div>
                </div>
                <div className="px-3 py-2">
                  <div className="mb-2 flex items-center justify-between">
                    <div>
                      <h4 className="text-[11px] font-medium text-slate-900">
                        DESTINY-Breast04 Extension
                      </h4>
                      <p className="mt-1 text-[9px] text-slate-600">
                        T-DXd in HER2-low metastatic breast cancer
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-[8px] text-slate-500">Match score</p>
                      <p className="text-[12px] font-semibold text-purple-600">
                        87%
                      </p>
                    </div>
                  </div>

                  <div className="mb-1 rounded-lg border border-purple-200 bg-purple-50 p-2">
                    <div className="mb-2 flex items-center gap-2">
                      <svg
                        className="h-4 w-4 text-purple-600"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      <span className="text-[8px] font-medium text-purple-900">
                        Key eligibility met
                      </span>
                    </div>
                    <ul className="space-y-1 text-[8px] text-purple-800">
                      <li className="flex items-center gap-1">
                        <span className="text-emerald-600">✓</span> ER+/PR+
                        confirmed
                      </li>
                      <li className="flex items-center gap-1">
                        <span className="text-emerald-600">✓</span> Prior
                        endocrine therapy
                      </li>
                      <li className="flex items-center gap-1">
                        <span className="text-amber-600">⚠</span> Genomic assay
                        pending
                      </li>
                    </ul>
                  </div>

                  <div className="flex items-center gap-2">
                    <button className="rounded-lg bg-purple-600 p-2 text-[10px] font-medium text-white hover:bg-purple-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-500">
                      View criteria
                    </button>
                    <button className="rounded-lg border border-slate-300 p-2 text-[10px] font-medium text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-500">
                      Share to patient
                    </button>
                  </div>
                </div>
              </motion.div>
            </div>

            <div className="scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100 w-1/3 space-y-2 overflow-y-auto border-l border-slate-200 pl-2">
              <motion.div
                className="rounded-lg bg-white ring-1 ring-slate-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.45 }}
              >
                <div className="border-b border-slate-200 px-4 py-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-[10px] font-semibold text-slate-900">
                      Tasks
                    </h3>
                    <button className="text-[8px] font-medium text-pink-600 hover:text-pink-700">
                      + Add task
                    </button>
                  </div>
                  <div
                    className="mt-2 inline-flex overflow-hidden rounded-full border border-gray-300"
                    role="group"
                  >
                    {(["mine", "team", "all"] as const).map((filter) => (
                      <button
                        key={filter}
                        className={`px-2 py-0.5 text-[10px] font-medium ${
                          taskFilter === filter
                            ? "bg-gray-100 text-gray-900"
                            : "bg-white text-gray-600"
                        }`}
                      >
                        {filter.charAt(0).toUpperCase() + filter.slice(1)}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="divide-y divide-slate-200">
                  {tasks.map((task) => (
                    <div key={task.id} className="flex items-start gap-2 p-2">
                      <div className="mt-1">
                        <div
                          className={`h-2 w-2 rounded-full ${
                            task.status === "done"
                              ? "bg-emerald-500"
                              : task.status === "overdue"
                                ? "bg-red-500"
                                : "bg-amber-500"
                          }`}
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <p
                          className={`text-[10px] text-slate-900 ${
                            task.status === "done"
                              ? "text-slate-500 line-through"
                              : ""
                          }`}
                        >
                          {task.title}
                        </p>
                        <p className="mt-0.5 text-[8px] text-slate-500">
                          {task.status === "overdue" ? (
                            <span className="font-medium text-red-600">
                              Overdue
                            </span>
                          ) : (
                            <>Due {task.due}</>
                          )}{" "}
                          • {task.assignee}
                        </p>
                      </div>
                      <button
                        className="rounded-md border border-slate-300 px-2 py-1 text-[10px] hover:bg-slate-50"
                        onClick={() => {
                          if (task.status !== "done") {
                            setTasks((prev) =>
                              prev.map((t) =>
                                t.id === task.id
                                  ? { ...t, status: "done" as const }
                                  : t
                              )
                            );
                          }
                        }}
                        disabled={task.status === "done"}
                      >
                        {task.status === "done" ? "Done" : "Complete"}
                      </button>
                    </div>
                  ))}
                </div>

                <div className="border-t border-slate-200 p-2">
                  <div className="flex items-center gap-2">
                    <button className="rounded-md border border-slate-300 px-2 py-1 text-[8px] hover:bg-slate-50">
                      Bulk complete
                    </button>
                    <button className="rounded-md border border-slate-300 px-2 py-1 text-[8px] hover:bg-slate-50">
                      Reschedule
                    </button>
                    <button className="rounded-md border border-slate-300 px-2 py-1 text-[8px] hover:bg-slate-50">
                      Assign
                    </button>
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="rounded-lg bg-white ring-1 ring-slate-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <div className="border-b border-slate-200 px-4 py-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-[10px] font-semibold text-slate-900">
                      Note Composer
                    </h3>
                    <select className="rounded-md border border-slate-300 px-2 py-1 text-[8px] focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500">
                      <option value="assessment">Assessment & Plan</option>
                      <option value="regimen">Regimen change</option>
                      <option value="toxicity">Toxicity visit</option>
                    </select>
                  </div>
                </div>

                <div className="p-2">
                  <div className="mb-2 rounded-md border border-slate-200 bg-slate-50 p-1">
                    <p className="text-[9px] leading-relaxed text-slate-900">
                      <span className="font-medium">Assessment:</span> Patient
                      with metastatic breast cancer,{" "}
                      <button className="text-pink-600 underline hover:text-pink-700">
                        ER+/PR+, HER2– (Obs#A16)
                      </button>
                      , presenting for treatment planning.
                    </p>
                    <p className="mt-2 text-[9px] leading-relaxed text-slate-900">
                      <span className="font-medium">Plan:</span> Initiate
                      endocrine therapy + CDK4/6 inhibitor per protocol.{" "}
                      <button className="text-pink-600 underline hover:text-pink-700">
                        Prior response to AI noted (RX#L02)
                      </button>
                      . Monitor CBC q2w × 2 cycles, then monthly.
                    </p>
                  </div>

                  <div className="mb-2">
                    <p className="mb-2 text-[8px] font-medium text-slate-700">
                      Orders added to note:
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <span className="inline-flex items-center gap-1 rounded-full border border-emerald-300 bg-emerald-50 px-2 py-1 text-[8px] text-emerald-700">
                        <svg
                          className="h-3 w-3"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        CBC with diff
                      </span>
                      <span className="inline-flex items-center gap-1 rounded-full border border-emerald-300 bg-emerald-50 px-2 py-1 text-[8px] text-emerald-700">
                        <svg
                          className="h-3 w-3"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        LFTs
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <button className="flex-1 rounded-md bg-pink-600 px-2 py-1 text-[8px] font-medium text-white hover:bg-pink-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500">
                      Copy to EMR
                    </button>
                    <button className="flex-1 rounded-md border border-slate-300 px-2 py-1 text-[8px] font-medium text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-pink-500">
                      Save as FHIR
                    </button>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

SaferActionsView.displayName = "SaferActionsView";
export default SaferActionsView;
