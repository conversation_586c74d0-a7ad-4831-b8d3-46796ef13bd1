"use client";

import { motion, Variants } from "framer-motion";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>ck,
  Microscope,
  ShieldCheck,
  Stethoscope,
} from "lucide-react";
import React, { memo, useCallback, useMemo } from "react";

type FlowCardItem = {
  id: string;
  k: string;
  title: string;
  chips: string[];
  gradient: string;
  icon: React.ReactNode;
  branch?: "left" | "right" | "center";
};

type FlowGateItem = {
  id: string;
  gate: "TRUE_FALSE_SPLIT";
};

type FlowItem = FlowCardItem | FlowGateItem;

const IconGrad = memo(
  ({
    children,
    className = "",
  }: {
    children: React.ReactNode;
    className?: string;
  }) => (
    <div
      className={`grid h-7 w-7 flex-shrink-0 place-items-center rounded-full text-white shadow-inner shadow-black/20 ring-1 ring-white/40 ${className}`}
    >
      {children}
    </div>
  )
);
IconGrad.displayName = "IconGrad";

const Chip = memo(({ children }: { children: React.ReactNode }) => (
  <span className="rounded-md bg-white/80 px-1.5 py-0.5 text-[11px] font-medium text-slate-800 shadow-sm ring-1 ring-black/10">
    {children}
  </span>
));
Chip.displayName = "Chip";

const FlowCard = memo(
  ({
    k,
    title,
    chips,
    icon,
    gradient,
    onClick,
  }: {
    k: string;
    title: string;
    chips: string[];
    icon: React.ReactNode;
    gradient: string;
    onClick?: () => void;
  }) => (
    <button
      onClick={onClick}
      aria-label={`${k}: ${title}`}
      className="relative w-full max-w-sm rounded-lg bg-white/95 p-3 shadow-[0_6px_20px_rgba(2,6,23,0.08)] ring-1 ring-black/5 backdrop-blur transition-all duration-200 hover:scale-[1.01] hover:shadow-[0_10px_28px_rgba(2,6,23,0.12)] focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-1"
    >
      <div className="flex items-start gap-2.5">
        <IconGrad className={`bg-gradient-to-br ${gradient}`}>{icon}</IconGrad>
        <div className="flex-1 text-left">
          <div className="text-[9px] font-semibold tracking-[0.12em] text-slate-400">
            {k}
          </div>
          <div className="mt-0.5 text-[13px] font-medium leading-tight text-slate-800">
            {title}
          </div>
          <div className="mt-1.5 flex flex-wrap gap-1">
            {chips.map((t) => (
              <Chip key={t}>{t}</Chip>
            ))}
          </div>
        </div>
      </div>
    </button>
  )
);
FlowCard.displayName = "FlowCard";

const SplitGate = memo(() => (
  <div className="relative flex h-16 items-center justify-center">
    <div className="absolute left-1/2 top-0 h-full w-px -translate-x-1/2 bg-slate-300/40" />
    <div className="flex w-full justify-center gap-12">
      <div className="flex flex-col items-center">
        <div className="h-px w-16 bg-slate-300/60" />
        <span className="mt-1 rounded-full bg-slate-800/95 px-2.5 py-0.5 text-[10px] font-semibold text-white">
          TRUE
        </span>
      </div>
      <div className="flex flex-col items-center">
        <div className="h-px w-16 bg-slate-300/60" />
        <span className="mt-1 rounded-full bg-slate-800/60 px-2.5 py-0.5 text-[10px] font-semibold text-white/90">
          FALSE
        </span>
      </div>
    </div>
  </div>
));
SplitGate.displayName = "SplitGate";

const OncologyFlow: React.FC = memo(
  ({ className = "", , enableAnimations = true }) => {

    const steps: FlowItem[] = useMemo(
      () => [
        {
          id: "intake",
          k: "PATIENT INTAKE & CONSENT",
          title: "Patient verified & consented",
          chips: ["ABDM linked", "Outside records attached"],
          icon: <ClipboardCheck className="h-3.5 w-3.5" strokeWidth={2} />,
          gradient: "from-cyan-400 via-blue-400 to-violet-400",
          branch: "center",
        },
        {
          id: "reconcile",
          k: "RECONCILE RECORDS",
          title: "Unify history, labs & imaging into one view",
          chips: ["Latest regimen & lines", "Key labs & imaging summary"],
          icon: <Stethoscope className="h-3.5 w-3.5" strokeWidth={2} />,
          gradient: "from-fuchsia-400 via-violet-400 to-cyan-400",
          branch: "center",
        },
        { id: "gate", gate: "TRUE_FALSE_SPLIT" },
        {
          id: "prep",
          k: "TUMOR BOARD PREP",
          title: "Clinical summary ready",
          chips: ["Stage & biomarkers", "Performance status"],
          icon: <Microscope className="h-3.5 w-3.5" strokeWidth={2} />,
          gradient: "from-indigo-400 via-blue-400 to-cyan-400",
          branch: "left",
        },
        {
          id: "plan",
          k: "PLAN & SAFETY",
          title: "Proposed regimen with checks",
          chips: ["DDI/allergy alerts", "Trial matches"],
          icon: <ShieldCheck className="h-3.5 w-3.5" strokeWidth={2} />,
          gradient: "from-pink-400 via-fuchsia-400 to-violet-400",
          branch: "right",
        },
      ],
      []
    );

    const itemVariants: Variants = {
      hidden: { opacity: 0, y: 12 },
      visible: (i: number) => ({
        opacity: 1,
        y: 0,
        transition: {
          duration: 0.35,
          ease: [0.22, 1, 0.36, 1],
          delay: i * 0.06,
        },
      }),
    };

    return (
      <section
        className={`relative mx-auto flex flex-col items-center justify-center overflow-visible ${className}`}
        role="img"
        aria-label="Oncology workflow diagram"
      >
        <div
          aria-hidden
          className="absolute inset-y-0 flex w-full justify-between px-[10%]"
        >
          <div className="w-px bg-slate-300/30" />
          <div className="w-px bg-slate-300/40" />
          <div className="w-px bg-slate-300/30" />
        </div>

        <div className="relative z-10 flex w-full max-w-2xl flex-col items-center gap-4 px-4 sm:px-6">
          {steps.map((s, i) => {
            const isGate = (s as FlowGateItem).gate === "TRUE_FALSE_SPLIT";
            const card = s as FlowCardItem;

            return (
              <motion.div
                key={s.id}
                initial={enableAnimations ? "hidden" : undefined}
                whileInView={enableAnimations ? "visible" : undefined}
                viewport={{ once: true, amount: 0.6 }}
                custom={i}
                variants={itemVariants}
                className={`relative flex w-full justify-center ${
                  card.branch === "left"
                    ? "lg:justify-start"
                    : card.branch === "right"
                      ? "lg:justify-end"
                      : "lg:justify-center"
                }`}
              >
                {isGate ? (
                  <SplitGate />
                ) : (
                  <FlowCard
                    k={card.k}
                    title={card.title}
                    chips={card.chips}
                    gradient={card.gradient}
                    icon={card.icon}
                    onClick={() => handleCardClick(card.id)}
                  />
                )}
              </motion.div>
            );
          })}
        </div>
      </section>
    );
  }
);
OncologyFlow.displayName = "OncologyFlow";

export default OncologyFlow;
