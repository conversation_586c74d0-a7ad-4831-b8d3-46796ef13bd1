import { InteractiveButton } from "@/components/animations";
import HeroBackground from "@/components/specialized/HeroBackground";
import OncologyFlow from "@/components/specialized/OncologyFlow";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { fadeVariants, staggerContainer } from "@/lib/animations";
import { motion } from "framer-motion";
import React, { useCallback, useState } from "react";

const Hero: React.FC = () => {
  const [email, setEmail] = useState("");
  const [open, setOpen] = useState(false);
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleWatch = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault();
      if (!/[^\s@]+@[^\s@]+\.[^\s@]+/.test(email)) {
        setError("Please enter a valid work email.");
        return;
      }

      setError("");
      setIsSubmitting(true);
      try {
        const payload = { source: "demo", email: email.trim() };
        const res = await fetch(
          `${import.meta.env.VITE_NODE_API_URL}/api/lead`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          }
        );

        const data = await res.json();
        if (!res.ok) throw new Error(data.error || "Failed to submit");

        setOpen(true);
      } catch {
        setError("Submission failed. Please try again.");
      } finally {
        setIsSubmitting(false);
      }
    },
    [email]
  );

  return (
    <section
      id="hero"
      className="relative flex min-h-screen flex-col items-center justify-center overflow-hidden lg:flex-row"
    >
      <HeroBackground />

      <div className="relative z-20 flex w-full flex-col items-center justify-center space-y-6 p-8 text-center md:p-12 lg:w-1/2 lg:items-start lg:p-20 lg:text-left">
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          animate="visible"
          className="w-full max-w-xl space-y-6"
        >
          <motion.h1
            variants={fadeVariants.up}
            className="text-display-hero font-semibold leading-tight"
          >
            <span className="block">Stop hunting</span>
            <span className="block">
              for{" "}
              <em className="font-bold italic text-primary">patient data</em>
            </span>
            <span className="block">across systems.</span>
          </motion.h1>

          <motion.p
            variants={fadeVariants.left}
            className="text-body-large mx-auto max-w-lg text-muted-foreground lg:mx-0"
          >
            Get complete clinical context in{" "}
            <strong className="text-foreground">60 seconds</strong>. AI-powered
            summaries from all your systems, ready for tumor board decisions.
          </motion.p>

          <motion.form
            variants={fadeVariants.right}
            onSubmit={handleWatch}
            className="mx-auto mt-8 flex w-full max-w-md flex-col gap-3 sm:flex-row lg:mx-0"
          >
            <div className="flex-grow">
              <label htmlFor="hero-email" className="sr-only">
                Enter your work email
              </label>
              <Input
                id="hero-email"
                type="email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  if (error) setError("");
                }}
                placeholder="Enter work email"
                className={`h-11 w-full lg:w-80 ${
                  error ? "border-destructive focus:ring-destructive" : ""
                }`}
                aria-invalid={!!error}
                aria-describedby="email-error"
              />
              {error && (
                <p
                  id="email-error"
                  className="mt-2 text-left text-sm text-destructive"
                >
                  {error}
                </p>
              )}
            </div>

            <InteractiveButton
              type="submit"
              size="lg"
              variant="hero"
              disabled={isSubmitting}
              className="shrink-0"
            >
              {isSubmitting ? "Submitting..." : "Watch the demo"}
            </InteractiveButton>
          </motion.form>
        </motion.div>
      </div>

      <div className="relative z-10 flex h-full w-full items-center justify-center md:h-[500px] lg:h-auto lg:w-1/2">
        <OncologyFlow />
      </div>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="aspect-video h-auto w-[85vw] max-w-4xl overflow-hidden rounded-xl border-0 p-0 shadow-2xl">
          {open && (
            <iframe
              key="demo-video"
              className="h-full w-full"
              src={import.meta.env.VITE_YOUTUBE_VIDEO}
              title="Entheory Demo"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            />
          )}
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default Hero;
