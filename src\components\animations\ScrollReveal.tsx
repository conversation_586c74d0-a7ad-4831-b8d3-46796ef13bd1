import { cn } from "@/lib/utils";
import { motion, Variants } from "framer-motion";
import React, { memo, ReactNode } from "react";

type AnimationType =
  | "fade-up"
  | "fade-down"
  | "fade-left"
  | "fade-right"
  | "scale-in"
  | "rotate-in";

const animationVariants: Record<AnimationType, Variants> = {
  "fade-up": {
    hidden: { y: 40, opacity: 0 },
    visible: { y: 0, opacity: 1 },
  },
  "fade-down": {
    hidden: { y: -40, opacity: 0 },
    visible: { y: 0, opacity: 1 },
  },
  "fade-left": {
    hidden: { x: -40, opacity: 0 },
    visible: { x: 0, opacity: 1 },
  },
  "fade-right": {
    hidden: { x: 40, opacity: 0 },
    visible: { x: 0, opacity: 1 },
  },
  "scale-in": {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { scale: 1, opacity: 1 },
  },
  "rotate-in": {
    hidden: { rotate: -10, scale: 0.9, opacity: 0 },
    visible: { rotate: 0, scale: 1, opacity: 1 },
  },
};

interface ScrollRevealProps {
  children: ReactNode;
  animation?: AnimationType;
  delay?: number;
  threshold?: number;
  className?: string;
  triggerOnce?: boolean;
}

export const ScrollReveal: React.FC<ScrollRevealProps> = memo(
  ({
    children,
    animation = "fade-up",
    delay = 0.25,
    threshold = 0.15,
    className,
    triggerOnce = true,
  }) => {
    return (
      <motion.div
        className={cn(className)}
        variants={animationVariants[animation]}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: triggerOnce, amount: threshold }}
        transition={{ duration: 0.8, ease: "easeOut", delay }}
      >
        {children}
      </motion.div>
    );
  }
);

ScrollReveal.displayName = "ScrollReveal";

interface StaggeredRevealProps {
  children: ReactNode[];
  animation?: AnimationType;
  delay?: number;
  threshold?: number;
  className?: string;
  triggerOnce?: boolean;
}

export const StaggeredReveal: React.FC<StaggeredRevealProps> = memo(
  ({
    children,
    animation = "fade-left",
    delay = 1,
    threshold = 0.15,
    className,
    triggerOnce = true,
  }) => {
    const containerVariants: Variants = {
      hidden: {},
      visible: {
        transition: {
          staggerChildren: delay,
        },
      },
    };

    return (
      <motion.div
        className={cn("space-y-4", className)}
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: triggerOnce, amount: threshold }}
      >
        {children.map((child, index) => (
          <motion.div key={index} variants={animationVariants[animation]}>
            {child}
          </motion.div>
        ))}
      </motion.div>
    );
  }
);

StaggeredReveal.displayName = "StaggeredReveal";
