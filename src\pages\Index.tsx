import { ScrollReveal } from "@/components/animations";
import Header from "@/components/common/Header";
import InterSectionTransition from "@/components/common/InterSectionTransition";
import PageTransitionController from "@/components/common/PageTransitionController";
import { fadeVariants, headerVariants } from "@/lib/animations";
import { motion } from "framer-motion";
import React, { lazy, memo, Suspense } from "react";

const Hero = lazy(() => import("@/components/sections/Hero"));
const AuraStack = lazy(() => import("@/components/specialized/AuraStack"));
const AuraBranches = lazy(
  () => import("@/components/specialized/AuraBranches")
);
const Scrollytelling = lazy(
  () => import("@/components/sections/Scrollytelling")
);

const LazyFallback = memo(() => <div className="min-h-screen" />);
LazyFallback.displayName = "LazyFallback";

const SECTIONS = [
  { id: "hero", component: Hero, skipTransition: true },
  {
    id: "aura-stack",
    component: AuraStack,
    transition: {
      from: "#hero",
      gradient: "linear-gradient(180deg, #FDFEFE 0%, #FFF9F2 100%)",
    },
  },
  {
    id: "aura-branches",
    component: AuraBranches,
    transition: {
      from: "#aura-stack",
      gradient: "linear-gradient(180deg, #FFF9F2 0%, #D8F8FF 100%)",
    },
  },
  {
    id: "scrollytelling",
    component: Scrollytelling,
    transition: {
      from: "#system-standards",
      gradient: "linear-gradient(180deg, #F6F7FB 0%, #FFFFFF 100%)",
    },
  },
];

const Index: React.FC = memo(() => {
  return (
    <>
      <PageTransitionController>
        <motion.header variants={headerVariants}>
          <Header />
        </motion.header>

        <motion.main className="relative" variants={fadeVariants.up}>
          {SECTIONS.map(
            ({ id, component: Component, transition, skipTransition }) => {
              const sectionContent = (
                <Suspense fallback={<LazyFallback />}>
                  <Component />
                </Suspense>
              );
              return (
                <React.Fragment key={id}>
                  {transition && !skipTransition && (
                    <InterSectionTransition
                      from={transition.from}
                      to={`#${id}`}
                      gradient={transition.gradient}
                    />
                  )}
                  <section id={id} className="relative">
                    <ScrollReveal>{sectionContent}</ScrollReveal>
                  </section>
                </React.Fragment>
              );
            }
          )}
        </motion.main>
      </PageTransitionController>
    </>
  );
});

Index.displayName = "Index";

export default Index;
