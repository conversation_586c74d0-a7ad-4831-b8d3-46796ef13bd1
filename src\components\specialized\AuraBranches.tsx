"use client";

import { cn } from "@/lib/utils";
import { motion, useScroll, useTransform } from "framer-motion";
import { ArrowDown } from "lucide-react";
import React, { memo, useEffect, useRef, useState } from "react";
import AutoFetchView from "./AutoFetchView";
import BoardSummaryView from "./BoardSummaryView";
import SaferActionsView from "./SaferActionsView";
import TimelineView from "./TimelineView";

export interface Branch {
  id: string;
  title: string;
  subtitle: string;
  bgColor: string;
}

const DEFAULT_BRANCHES: Branch[] = [
  {
    id: "auto-fetch",
    title: "AUTO-FETCH",
    subtitle: "& CONSENT",
    bgColor: "linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)",
  },
  {
    id: "timeline",
    title: "ONE TIMELINE",
    subtitle: "ONE PATIENT",
    bgColor: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
  },
  {
    id: "board-ready",
    title: "BOARD-READY",
    subtitle: "SUMMARY",
    bgColor: "linear-gradient(135deg, #fef9c3 0%, #fef08a 50%, #fefce8 100%)",
  },
  {
    id: "safer-actions",
    title: "SAFER ACTIONS",
    subtitle: "& FOLLOW-UPS",
    bgColor: "linear-gradient(135deg, #ec4899 0%, #db2777 100%)",
  },
];

const AuraBranches: React.FC<{ branches?: Branch[] }> = memo(
  ({ branches = DEFAULT_BRANCHES }) => {
    const [activeTab, setActiveTab] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);

    const { scrollYProgress } = useScroll({
      target: containerRef,
      offset: ["start start", "end end"],
    });

    const sectionHeightVh = branches.length * 100;

    const normalizedProgress = useTransform(scrollYProgress, (p: number) => {
      const clamped = Math.max(0, Math.min(0.9999, p));
      return clamped * branches.length;
    });

    useEffect(() => {
      const unsubscribe = normalizedProgress.on("change", (value) => {
        const newIndex = Math.min(branches.length - 1, Math.floor(value));
        if (newIndex !== activeTab) setActiveTab(newIndex);
      });
      return () => unsubscribe();
    }, [normalizedProgress, activeTab, branches.length]);

    const activeBranch = branches[activeTab];

    return (
      <section
        ref={containerRef}
        className="relative w-full"
        style={{ height: `${sectionHeightVh}vh` }}
      >
        <div className="sticky top-0 flex h-screen w-full flex-col overflow-hidden">
          <motion.div
            className="absolute inset-0"
            style={{ backgroundImage: activeBranch.bgColor }}
          />
          <div className="absolute inset-0 opacity-10">
            <div
              className="absolute inset-0"
              style={{
                backgroundImage: `repeating-linear-gradient(
                45deg,
                transparent,
                transparent 35px,
                rgba(255,255,255,0.05) 35px,
                rgba(255,255,255,0.05) 70px
              )`,
              }}
            />
          </div>

          <div className="relative z-10 flex-1 overflow-hidden">
            <div className="flex min-h-screen w-full flex-col px-2">
              <div style={{ display: activeTab === 0 ? "block" : "none" }}>
                <AutoFetchView />
              </div>
              <div style={{ display: activeTab === 1 ? "block" : "none" }}>
                <TimelineView />
              </div>
              <div style={{ display: activeTab === 2 ? "block" : "none" }}>
                <BoardSummaryView />
              </div>
              <div style={{ display: activeTab === 3 ? "block" : "none" }}>
                <SaferActionsView />
              </div>
            </div>
          </div>

          <motion.div
            className={cn(
              "absolute bottom-4",
              activeTab === 0 || activeTab === 2 ? "right-40" : "left-36"
            )}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
          >
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className={cn(
                "flex flex-col items-center gap-1",
                activeTab === 2 ? "text-black" : "text-white"
              )}
            >
              <span className="text-[14px] font-extrabold uppercase tracking-wider">
                Scroll
              </span>
              <ArrowDown className="h-6 w-6" />
            </motion.div>
          </motion.div>
        </div>
      </section>
    );
  }
);

AuraBranches.displayName = "AuraBranches";

export default AuraBranches;
