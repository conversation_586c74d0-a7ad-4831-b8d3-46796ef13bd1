import { useScroll, useTransform } from "framer-motion";
import React, { memo, useEffect, useRef } from "react";

interface Props {
  from: string | undefined;
  to: string;
  gradient?: string;
}

export const InterSectionTransition: React.FC<Props> = memo(
  ({ from, to, gradient }) => {
    const ref = useRef<HTMLDivElement | null>(null);

    const { scrollYProgress } = useScroll({
      target: ref,
      offset: ["start end", "end start"],
    });

    const fromOpacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);
    const fromScale = useTransform(scrollYProgress, [0, 0.5], [1, 0.985]);
    const toOpacity = useTransform(scrollYProgress, [0.5, 1], [0, 1]);
    const toScale = useTransform(scrollYProgress, [0.5, 1], [0.985, 1]);

    useEffect(() => {
      if (from === undefined) return;

      const fromEl = document.querySelector(from) as HTMLElement | null;
      const toEl = document.querySelector(to) as HTMLElement | null;

      if (!fromEl || !toEl) {
        if (ref.current) ref.current.style.height = "0px";
        return;
      }

      const unsubFromOpacity = fromOpacity.on("change", (latest) => {
        fromEl.style.opacity = `${latest}`;
      });
      const unsubFromScale = fromScale.on("change", (latest) => {
        fromEl.style.transform = `scale(${latest})`;
      });
      const unsubToOpacity = toOpacity.on("change", (latest) => {
        toEl.style.opacity = `${latest}`;
      });
      const unsubToScale = toScale.on("change", (latest) => {
        toEl.style.transform = `scale(${latest})`;
      });

      return () => {
        unsubFromOpacity();
        unsubFromScale();
        unsubToOpacity();
        unsubToScale();
        fromEl.style.opacity = "";
        fromEl.style.transform = "";
      };
    }, [from, to, fromOpacity, fromScale, toOpacity, toScale]);
    return (
      <div ref={ref} aria-hidden className="relative z-[-1] h-[25vh] w-full">
        {gradient && (
          <div
            className="pointer-events-none absolute inset-0"
            style={{ background: gradient }}
          />
        )}
      </div>
    );
  }
);

InterSectionTransition.displayName = "InterSectionTransition";

export default InterSectionTransition;
