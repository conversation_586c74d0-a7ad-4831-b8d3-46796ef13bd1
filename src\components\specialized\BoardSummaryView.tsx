import { motion } from "framer-motion";
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

const stage = {
  disease: "Breast cancer",
  stage: "Stage IIA",
  tnm: "T2N0M0",
  range: "Apr–Aug 2025",
  system: "AJCC 8th",
  stagedOn: "24 Aug 2025",
};
const biomarkers = [
  {
    name: "ER",
    value: "90%",
    unit: "",
    method: "IHC",
    source: "P",
    specimen: "Biopsy",
    collectedAt: "20 Aug",
    reportedAt: "21 Aug",
  },
  {
    name: "PR",
    value: "80%",
    unit: "",
    method: "IHC",
    source: "P",
    specimen: "Biopsy",
    collectedAt: "20 Aug",
    reportedAt: "21 Aug",
  },
  {
    name: "HER2",
    value: "0",
    unit: " (IHC)",
    method: "IHC",
    source: "P",
    specimen: "Biopsy",
    collectedAt: "20 Aug",
    reportedAt: "21 Aug",
  },
];
const lines = [
  {
    line: "1L",
    regimen: "AC → Paclitaxel",
    start: "Apr 2025",
    stop: "Aug 2025",
    response: "PR",
    evidence: ["Onc consult", "Orders", "Dispense"],
  },
  {
    line: "2L",
    regimen: "CDK4/6 + AI (Proposed)",
    start: "—",
    stop: "",
    response: "—",
    evidence: ["Plan"],
  },
];
const imagingList = [
  {
    date: "22 Aug 2025",
    modality: "CT Chest",
    impression: "No new lesions; nodes stable",
    frames: ["Axial 1", "Axial 2", "Coronal 1", "Sagittal 1"],
  },
  {
    date: "10 Aug 2025",
    modality: "Mammogram",
    impression: "Right breast mass 3.1cm",
    frames: ["CC left", "MLO left", "CC right", "MLO right"],
  },
];
const evidence = [
  {
    id: "path-biopsy",
    title: "Pathology report",
    source: "EMR",
    kind: "path",
    specimen: "Core biopsy",
    collectedAt: "20 Aug",
    reportedAt: "21 Aug",
    quality: "complete",
  },
  {
    id: "onc-consult",
    title: "Oncology consult note",
    source: "EMR",
    kind: "note",
    collectedAt: "24 Aug",
    reportedAt: "24 Aug",
    quality: "partial",
  },
  {
    id: "lab-erprher2",
    title: "ER/PR/HER2 panel",
    source: "LIS",
    kind: "lab",
    specimen: "Biopsy",
    collectedAt: "20 Aug",
    reportedAt: "21 Aug",
    quality: "complete",
  },
  {
    id: "img-ct",
    title: "CT report",
    source: "PACS",
    kind: "imaging",
    collectedAt: "22 Aug",
    reportedAt: "22 Aug",
    quality: "conflict",
  },
];

const BoardSummaryView: React.FC<{ isActive?: boolean }> = memo(
  ({ isActive = true }) => {
    const [display, setDisplay] = useState<"summary" | "full">("summary");
    const [provQuery, setProvQuery] = useState("");
    const [openImage, setOpenImage] = useState<any | null>(null);
    const [openReport, setOpenReport] = useState<any | null>(null);

    const ASSEMBLY_KEY = "m3_assembly_samples";
    const MAX_SAMPLES = 12;
    const [timerStart, setTimerStart] = useState<number | null>(null);
    const [assemblySamples, setAssemblySamples] = useState<number[]>([]);

    const loadSamples = useCallback((): number[] => {
      try {
        if (typeof window === "undefined") return [];
        const raw = localStorage.getItem(ASSEMBLY_KEY);
        if (!raw) return [];
        const arr = JSON.parse(raw);
        return Array.isArray(arr)
          ? arr.filter((n: any) => typeof n === "number")
          : [];
      } catch {
        return [];
      }
    }, []);

    const saveSamples = useCallback((arr: number[]) => {
      if (typeof window === "undefined") return;
      localStorage.setItem(
        ASSEMBLY_KEY,
        JSON.stringify(arr.slice(-MAX_SAMPLES))
      );
    }, []);

    const recordAssemblyTime = useCallback(() => {
      if (timerStart == null) return;
      const durationSec = Math.max(
        1,
        Math.round((Date.now() - timerStart) / 1000)
      );
      setAssemblySamples((prev) => {
        const next = [...prev, durationSec].slice(-MAX_SAMPLES);
        saveSamples(next);
        return next;
      });
      setTimerStart(null);
    }, [timerStart, saveSamples]);

    useEffect(() => {
      if (!isActive) return;
      setTimerStart(Date.now());
      setAssemblySamples(loadSamples());
    }, [isActive, loadSamples]);

    const [selectedProvId, setSelectedProvId] = useState<string | null>(null);
    const provRefs = useRef<Record<string, HTMLDivElement | null>>({});
    const highlightMatch = (text: string): React.ReactNode => {
      if (!provQuery) return text;
      const idx = text.toLowerCase().indexOf(provQuery.toLowerCase());
      if (idx === -1) return text;
      const before = text.slice(0, idx);
      const match = text.slice(idx, idx + provQuery.length);
      const after = text.slice(idx + provQuery.length);
      return (
        <>
          {before}
          <mark className="rounded bg-yellow-200 px-0.5">{match}</mark>
          {after}
        </>
      );
    };

    const [debouncedProvQuery, setDebouncedProvQuery] = useState(provQuery);
    useEffect(() => {
      const id = window.setTimeout(() => setDebouncedProvQuery(provQuery), 120);
      return () => window.clearTimeout(id);
    }, [provQuery]);

    const filteredEvidence = useMemo(() => {
      const q = debouncedProvQuery.trim().toLowerCase();
      if (!q) return evidence;
      return evidence.filter((e) =>
        [e.title, e.source, e.specimen || "", e.kind]
          .join(" ")
          .toLowerCase()
          .includes(q)
      );
    }, []);

    const imageModalRef = useRef<HTMLDivElement>(null);
    const reportDrawerRef = useRef<HTMLDivElement>(null);
    const lastFocusedRef = useRef<HTMLElement | null>(null);

    const handleTrapKey = (
      e: React.KeyboardEvent,
      containerRef: React.RefObject<HTMLElement>
    ) => {
      if (e.key !== "Tab") return;
      const container = containerRef.current;
      if (!container) return;
      const focusableSelectors =
        'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])';
      const nodes = Array.from(
        container.querySelectorAll<HTMLElement>(focusableSelectors)
      ).filter((el) => !el.hasAttribute("disabled"));
      if (nodes.length === 0) return;
      const first = nodes[0];
      const last = nodes[nodes.length - 1];
      const current = document.activeElement as HTMLElement;
      if (e.shiftKey) {
        if (current === first || !container.contains(current)) {
          e.preventDefault();
          last.focus();
        }
      } else {
        if (current === last) {
          e.preventDefault();
          first.focus();
        }
      }
    };

    useEffect(() => {
      if (!isActive) return;
      const onKey = (e: KeyboardEvent) => {
        const key = e.key.toLowerCase();
        if (key === "f") {
          setDisplay((prev) => {
            const next = prev === "summary" ? "full" : "summary";
            return next;
          });
        }
        if (e.key === "Escape") {
          if (openImage) {
            setOpenImage(null);
            e.preventDefault();
          } else if (openReport) {
            setOpenReport(null);
            e.preventDefault();
          }
        }
      };
      window.addEventListener("keydown", onKey);
      return () => window.removeEventListener("keydown", onKey);
    }, [isActive, openImage, openReport]);

    useEffect(() => {
      if (openImage) {
        lastFocusedRef.current =
          (document.activeElement as HTMLElement) || null;
        const c = imageModalRef.current;
        setTimeout(() => {
          const first = c?.querySelector<HTMLElement>(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          );
          first?.focus();
        }, 0);
      }
    }, [openImage]);

    useEffect(() => {
      if (openReport) {
        lastFocusedRef.current =
          (document.activeElement as HTMLElement) || null;
        const c = reportDrawerRef.current;
        setTimeout(() => {
          const first = c?.querySelector<HTMLElement>(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          );
          first?.focus();
        }, 0);
      }
    }, [openReport]);

    useEffect(() => {
      if (!openImage && !openReport && lastFocusedRef.current) {
        lastFocusedRef.current.focus();
        lastFocusedRef.current = null;
      }
    }, [openImage, openReport]);

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.4, ease: [0.22, 1, 0.36, 1] }}
        className="flex h-full flex-col items-center justify-center gap-6 px-4 pb-4 pt-20 md:flex-row"
      >
        <div className="flex w-full max-w-md flex-col items-start text-left md:order-last md:w-1/4">
          <div className="inline-flex items-center gap-2 rounded-[20px] border border-white/70 bg-white/90 px-5 py-2.5 shadow-[0_8px_20px_rgba(0,0,0,0.1)] backdrop-blur-2xl">
            <div
              className="h-1.5 w-1.5 rounded-full"
              style={{ backgroundColor: "#926d00" }}
            />
            <div className="flex items-baseline gap-1">
              <span className="text-[10px] font-bold tracking-wider text-slate-800">
                BOARD-READY
              </span>
              <span className="text-[8px] font-medium tracking-wide text-slate-600">
                SUMMARY
              </span>
            </div>
          </div>

          <h2 className="mt-6 text-6xl font-light leading-[1.05] text-teal-800">
            <span className="font-semibold">Board-Ready,</span>
            <br />
            <span className="italic">auditable in seconds</span>
          </h2>
          <div className="mt-6 max-w-md">
            <p className="text-[14px] font-light text-slate-700">
              Auditable draft for tumor boards—stage, biomarkers, and past lines
              with citations to source documents.
            </p>
            <div
              role="list"
              aria-label="Capabilities"
              className="mt-4 flex flex-wrap gap-2"
            >
              {["Board export", "Gaps highlighted", "Human-in-loop"].map(
                (cap) => (
                  <span
                    key={cap}
                    role="listitem"
                    className="inline-flex items-center gap-1 rounded-full border border-white/60 bg-white/90 px-3 py-1 text-[10px] font-medium text-slate-800 backdrop-blur-md focus:outline-none focus-visible:ring-2 focus-visible:ring-white/80"
                    aria-label={cap}
                  >
                    {cap}
                  </span>
                )
              )}
            </div>
          </div>
        </div>

        <div className="relative w-full flex-1 md:order-first md:w-2/3">
          <div className="relative min-h-0 w-full flex-1 overflow-hidden rounded-xl bg-gray-50 shadow-2xl ring-1 ring-slate-200/60">
            <div className="border-b border-slate-200 bg-white/80 px-4 py-2 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <img
                      src="/logo.png"
                      alt="Entheory"
                      className="h-6 w-auto"
                    />
                  </div>
                  <nav className="hidden items-center gap-6 sm:flex">
                    <button className="text-[10px] text-gray-600 hover:text-gray-900">
                      Records
                    </button>
                    <button className="text-[10px] text-gray-600 hover:text-gray-900">
                      Timeline
                    </button>
                    <button className="text-[10px] font-medium text-amber-600">
                      Summary
                    </button>
                    <button className="text-[10px] text-gray-600 hover:text-gray-900">
                      Actions
                    </button>
                  </nav>
                </div>
                <div className="flex items-center gap-3">
                  <button className="rounded-lg bg-amber-500 px-4 py-1.5 text-[10px] font-medium text-white hover:bg-amber-600 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/70">
                    View Now
                  </button>
                </div>
              </div>
            </div>

            <div className="flex h-full" style={{ maxHeight: "80vh" }}>
              <div className="w-2/3 space-y-3 p-2">
                <div className="h-full rounded-xl bg-white p-2 shadow-sm ring-1 ring-slate-200">
                  <div className="flex items-center justify-between border-b border-slate-200 pb-1">
                    <div className="flex flex-col">
                      <div className="text-[12px] font-semibold text-slate-900">
                        Draft for discussion
                      </div>
                      <div className="mt-0.5 text-[8px] text-slate-600">
                        {stage.disease} • {stage.stage} ({stage.tnm}) •{" "}
                        {stage.range}
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div
                        className="inline-flex overflow-hidden rounded-full border border-gray-300"
                        role="group"
                        aria-label="View mode"
                      >
                        <button
                          className={`px-3 py-1 text-[8px] ${display === "summary" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"} focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500`}
                          aria-pressed={display === "summary"}
                          onClick={() => {
                            setDisplay("summary");
                          }}
                        >
                          Summary
                        </button>
                        <button
                          className={`px-3 py-1 text-[8px] ${display === "full" ? "bg-gray-100 text-gray-900" : "bg-white text-gray-600"} focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500`}
                          aria-pressed={display === "full"}
                          onClick={() => {
                            setDisplay("full");
                          }}
                        >
                          Full
                        </button>
                      </div>
                      <button className="rounded-md border border-slate-300 px-2 py-1 text-[8px] hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                        Re‑sync
                      </button>
                    </div>
                  </div>

                  <section className="pt-2">
                    <div className="text-[8px] font-semibold uppercase tracking-wider text-slate-700">
                      Diagnosis & Stage
                    </div>
                    <div className="mt-1 flex flex-wrap gap-2">
                      <button
                        data-cite-id="primary_site"
                        className="rounded-full border border-slate-300 px-2 py-1 text-[8px] text-slate-700 hover:bg-slate-50"
                        onClick={() => {
                          const id = "path-biopsy";
                          const match = evidence.find((e) => e.id === id);
                          if (match) {
                            setOpenReport({
                              id: match.id,
                              title: match.title,
                              source: match.source,
                            });
                          }
                        }}
                      >
                        Primary site: Breast
                      </button>
                      <button
                        data-cite-id="tnm"
                        className="rounded-full border border-slate-300 px-2 py-1 text-[8px] text-slate-700 hover:bg-slate-50"
                        onClick={() => {
                          const id = "onc-consult";
                          const match = evidence.find((e) => e.id === id);
                          if (match) {
                            setOpenReport({
                              id: match.id,
                              title: match.title,
                              source: match.source,
                            });
                          }
                        }}
                      >
                        TNM: {stage.tnm}
                      </button>
                      <button
                        data-cite-id="system"
                        className="rounded-full border border-slate-300 px-2 py-1 text-[8px] text-slate-700 hover:bg-slate-50"
                        onClick={() => {
                          const id = "onc-consult";
                          const match = evidence.find((e) => e.id === id);
                          if (match) {
                            setOpenReport({
                              id: match.id,
                              title: match.title,
                              source: match.source,
                            });
                          }
                        }}
                      >
                        {stage.system}
                      </button>
                      <button
                        data-cite-id="staged_on"
                        className="rounded-full border border-slate-300 px-2 py-1 text-[8px] text-slate-700 hover:bg-slate-50"
                        onClick={() => {
                          const id = "onc-consult";
                          const match = evidence.find((e) => e.id === id);
                          if (match) {
                            setOpenReport({
                              id: match.id,
                              title: match.title,
                              source: match.source,
                            });
                          }
                        }}
                      >
                        Staged {stage.stagedOn}
                      </button>
                    </div>

                    {display === "full" && (
                      <div className="mt-3 rounded-lg bg-slate-50/70 p-3 text-[8px] text-slate-700 ring-1 ring-slate-200">
                        <div>
                          <span className="text-slate-500">System:</span>{" "}
                          {stage.system}
                        </div>
                        <div>
                          <span className="text-slate-500">Rationale:</span>{" "}
                          Tumor size 2–5 cm, no regional node involvement, no
                          distant mets.
                        </div>
                        <div>
                          <span className="text-slate-500">Source:</span>{" "}
                          Pathology 20 Aug · Imaging 22 Aug · Onc Consult 24 Aug
                        </div>
                      </div>
                    )}
                  </section>

                  <section className="pt-2">
                    <div className="text-[8px] font-semibold uppercase tracking-wider text-slate-700">
                      Biomarkers
                    </div>
                    <div className="mt-1 flex flex-wrap gap-2">
                      {biomarkers.map((bm) => (
                        <div key={bm.name} className="group relative">
                          <button
                            className="inline-flex items-center gap-1 rounded-full border border-slate-300 px-2 py-1 text-[8px] text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                            data-cite-id={`biomarker_${bm.name}`}
                            onClick={() => {
                              const id = "lab-erprher2";
                              const match = evidence.find((e) => e.id === id);
                              if (match) {
                                setOpenReport({
                                  id: match.id,
                                  title: match.title,
                                  source: match.source,
                                });
                              }
                            }}
                            aria-label={`${bm.name} ${bm.value}${bm.unit ? " " + bm.unit : ""}`}
                            aria-describedby={`tip-bio-${bm.name}`}
                          >
                            <span
                              className="inline-flex h-3.5 w-3.5 items-center justify-center rounded-full text-[8px] font-bold text-white"
                              style={{
                                background:
                                  bm.source === "P"
                                    ? "#6366f1"
                                    : bm.source === "L"
                                      ? "#10b981"
                                      : "#0ea5e9",
                              }}
                            >
                              {bm.source}
                            </span>
                            <span>
                              {bm.name} {bm.value}
                              {bm.unit ? bm.unit : ""}
                            </span>
                          </button>

                          <div
                            id={`tip-bio-${bm.name}`}
                            role="tooltip"
                            className="pointer-events-none absolute left-1/2 mt-1 w-64 -translate-x-1/2 rounded-md bg-white p-2 text-[8px] text-slate-700 opacity-0 shadow-lg ring-1 ring-slate-200 transition-opacity duration-150 group-focus-within:opacity-100 group-hover:opacity-100"
                          >
                            <div>
                              <span className="text-slate-500">Method:</span>{" "}
                              {bm.method}
                            </div>
                            {bm.specimen && (
                              <div>
                                <span className="text-slate-500">
                                  Specimen:
                                </span>{" "}
                                {bm.specimen}
                              </div>
                            )}
                            <div className="mt-1 grid grid-cols-2 gap-2">
                              <div>
                                <span className="text-slate-500">
                                  Collected:
                                </span>{" "}
                                {bm.collectedAt}
                              </div>
                              <div>
                                <span className="text-slate-500">
                                  Reported:
                                </span>{" "}
                                {bm.reportedAt}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </section>

                  <section className="pt-2">
                    <div className="text-[8px] font-semibold uppercase tracking-wider text-slate-700">
                      Lines of Therapy
                    </div>
                    <div className="mt-1 space-y-2">
                      {lines.map((ln) => (
                        <div
                          key={ln.line}
                          className="flex items-start justify-between rounded-lg bg-white p-1.5 ring-1 ring-slate-200"
                        >
                          <div className="flex min-w-0 items-start gap-2">
                            <span className="shrink-0 rounded-full bg-slate-100 px-2 py-0.5 text-[8px] text-slate-700 ring-1 ring-slate-200">
                              {ln.line}
                            </span>
                            <div className="min-w-0">
                              <div className="truncate text-[9px] text-slate-900">
                                {ln.regimen}
                              </div>
                              <div className="text-[8px] text-slate-600">
                                {ln.start}
                                {ln.stop ? ` – ${ln.stop}` : ""}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="text-[8px] text-emerald-700">
                              {ln.response}
                            </div>
                            <div className="hidden items-center gap-1 md:flex">
                              {ln.evidence.map((ev) => (
                                <button
                                  key={ev}
                                  className="rounded-full border border-slate-300 px-1.5 py-0.5 text-[8px] text-slate-700 hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                                >
                                  {ev}
                                </button>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </section>

                  <section className="pt-2">
                    <div className="text-[8px] font-semibold uppercase tracking-wider text-slate-700">
                      Imaging
                    </div>
                    <div className="mt-1 grid grid-cols-1 gap-3 md:grid-cols-2">
                      {imagingList.map((img) => (
                        <div
                          key={img.date + img.modality}
                          className="flex items-start justify-between rounded-lg bg-white p-3 ring-1 ring-slate-200"
                        >
                          <div className="min-w-0">
                            <div className="truncate text-[9px] text-slate-900">
                              {img.modality} · {img.date}
                            </div>
                            <div className="mt-0.5 truncate text-[8px] text-slate-600">
                              {img.impression}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              className="rounded-md border border-slate-300 p-1 text-[8px] hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                              onClick={() => {
                                setOpenImage(img);
                              }}
                            >
                              Open Viewer
                            </button>
                            <button
                              className="rounded-md border border-slate-300 p-1 text-[8px] hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                              onClick={() => {
                                const id = "img-ct";
                                const match = evidence.find((e) => e.id === id);
                                if (match) {
                                  setOpenReport({
                                    id: match.id,
                                    title: match.title,
                                    source: match.source,
                                  });
                                }
                              }}
                            >
                              Report →
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </section>

                  <section className="pt-2">
                    <div className="flex items-start justify-between rounded-lg bg-amber-50 px-2 py-1 ring-1 ring-amber-200">
                      <div className="flex items-start gap-2">
                        <span
                          className="mt-0.5 inline-block h-2 w-2 rounded-full bg-amber-500"
                          aria-hidden
                        />
                        <div>
                          <div className="text-[10px] font-medium text-amber-800">
                            We found 2 missing items before board
                          </div>
                          <div className="mt-1 flex flex-wrap gap-2">
                            <div className="inline-flex items-center gap-2 rounded-full bg-white px-2 py-1 text-[8px] text-amber-800 ring-1 ring-amber-200">
                              Genomic assay pending
                              <div className="flex items-center gap-1">
                                <button className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                                  Order
                                </button>
                                <button className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                                  Request
                                </button>
                                <button className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                                  Assign
                                </button>
                              </div>
                            </div>
                            <div className="inline-flex items-center gap-2 rounded-full bg-white px-2 py-1 text-[8px] text-amber-800 ring-1 ring-amber-200">
                              Echo due
                              <div className="flex items-center gap-1">
                                <button className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                                  Order
                                </button>
                                <button className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                                  Request
                                </button>
                                <button className="rounded-md px-1.5 py-0.5 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                                  Assign
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <button className="rounded-md px-2 py-1 text-[8px] text-amber-800 ring-1 ring-amber-200 hover:bg-amber-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                        Dismiss
                      </button>
                    </div>
                  </section>

                  <div className="mt-2 flex items-center justify-between border-t border-slate-200 pt-2">
                    <div className="relative inline-flex">
                      <button className="rounded-l-md bg-indigo-600 px-3 py-1 text-[8px] text-white hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/70">
                        Export
                      </button>
                      <button
                        className="rounded-r-md border-l border-white/30 bg-indigo-600 px-2 py-1 text-[8px] text-white hover:bg-indigo-700 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/70"
                        aria-label="More export options"
                      >
                        ▾
                      </button>
                    </div>
                    <div className="flex items-center gap-2">
                      <button className="rounded-md border border-slate-300 px-2 py-1 text-[8px] hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                        Send to board agenda
                      </button>
                      <button
                        className="rounded-md border border-emerald-300 bg-emerald-50 px-2 py-1 text-[8px] text-emerald-700 hover:bg-emerald-100 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                        onClick={() => {
                          recordAssemblyTime();
                        }}
                      >
                        Mark reviewed ✓
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="w-1/3 border-l border-slate-200 bg-slate-50/60 p-3">
                <div className="sticky top-2 space-y-2">
                  <div className="text-[12px] font-semibold text-slate-900">
                    Provenance & controls
                  </div>
                  <input
                    value={provQuery}
                    onChange={(e) => setProvQuery(e.target.value)}
                    placeholder="Search evidence..."
                    className="w-full rounded-md border border-slate-200 bg-white px-2 py-1 text-[8px] focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                  <div className="divide-y divide-slate-200 overflow-hidden rounded-lg bg-white ring-1 ring-slate-200">
                    {filteredEvidence.length === 0 && (
                      <div className="p-3 text-[8px] text-slate-600">
                        No evidence matches your search.
                      </div>
                    )}
                    {filteredEvidence.map((ev) => (
                      <div
                        key={ev.id}
                        ref={(el) => (provRefs.current[ev.id] = el)}
                        className={`flex items-start justify-between p-2 text-[8px] text-slate-700 ${selectedProvId === ev.id ? "bg-indigo-50 ring-1 ring-indigo-300" : ""}`}
                      >
                        <div className="min-w-0">
                          <div className="flex items-center gap-2">
                            <span
                              className={`inline-flex h-4 w-4 items-center justify-center rounded-full text-[8px] font-bold text-white ${ev.kind === "path" ? "bg-indigo-500" : ev.kind === "lab" ? "bg-emerald-600" : ev.kind === "imaging" ? "bg-sky-500" : "bg-slate-500"}`}
                            >
                              {ev.kind === "path"
                                ? "P"
                                : ev.kind === "lab"
                                  ? "L"
                                  : ev.kind === "imaging"
                                    ? "I"
                                    : "N"}
                            </span>
                            <span className="truncate text-slate-900">
                              {highlightMatch(ev.title) as any}
                            </span>
                          </div>
                          <div className="mt-1 text-slate-500">
                            {highlightMatch(ev.source) as any}
                            {ev.specimen ? (
                              <> · {highlightMatch(ev.specimen) as any}</>
                            ) : (
                              ""
                            )}
                          </div>
                          {(ev.collectedAt || ev.reportedAt) && (
                            <div className="mt-0.5 text-slate-500">
                              {ev.collectedAt
                                ? `Collected ${ev.collectedAt}`
                                : ""}
                              {ev.reportedAt
                                ? ` · Reported ${ev.reportedAt}`
                                : ""}
                            </div>
                          )}
                          {ev.quality && (
                            <div className="mt-1 flex items-center gap-2">
                              <span
                                className={`inline-flex items-center rounded-full px-1.5 py-0.5 text-[8px] ring-1 ${ev.quality === "complete" ? "bg-emerald-50 text-emerald-700 ring-emerald-200" : ev.quality === "partial" ? "bg-amber-50 text-amber-800 ring-amber-200" : "bg-red-50 text-red-700 ring-red-200"}`}
                              >
                                {ev.quality === "complete"
                                  ? "Complete"
                                  : ev.quality === "partial"
                                    ? "Partial"
                                    : "Conflict"}
                              </span>
                              {(ev.quality === "partial" ||
                                ev.quality === "conflict") && (
                                <button className="rounded-md border border-slate-300 px-1.5 py-0.5 text-[8px] hover:bg-slate-50">
                                  Resolve
                                </button>
                              )}
                            </div>
                          )}
                        </div>
                        <button
                          className="shrink-0 rounded-md border border-slate-300 px-2 py-1 text-[8px] hover:bg-slate-50"
                          style={{ alignSelf: "flex-start" }}
                          onClick={() => {
                            setSelectedProvId(ev.id);
                            setOpenReport({
                              id: ev.id,
                              title: ev.title,
                              source: ev.source,
                            });
                          }}
                        >
                          View full
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {openReport && (
              <div
                role="dialog"
                aria-modal={true}
                aria-labelledby="full-report-title"
                className="absolute inset-0 z-40"
                onKeyDown={(e) => handleTrapKey(e, reportDrawerRef)}
              >
                <div
                  className="absolute inset-0 bg-black/20"
                  onClick={() => setOpenReport(null)}
                />
                <div
                  ref={reportDrawerRef}
                  className="absolute right-0 top-0 h-full w-[520px] max-w-[50%] bg-white shadow-2xl ring-1 ring-slate-200"
                >
                  <div className="flex items-center justify-between border-b border-slate-200 px-3 py-1">
                    <h3
                      id="full-report-title"
                      className="truncate text-sm font-semibold text-slate-900"
                    >
                      {openReport.title}
                    </h3>
                    <button
                      className="text-slate-600 hover:text-slate-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                      onClick={() => setOpenReport(null)}
                      aria-label="Close"
                    >
                      ✕
                    </button>
                  </div>
                  <div className="grid h-[calc(100%-70px)] grid-cols-[1fr,220px] gap-3 p-2 text-sm">
                    <div className="h-full overflow-auto rounded-md bg-white p-3 text-[8px] ring-1 ring-slate-200">
                      <div className="mb-2 font-semibold text-slate-900">
                        Highlights
                      </div>
                      <ul className="space-y-2">
                        <li>TNM: T2N0M0</li>
                        <li>ER 90% (IHC)</li>
                        <li>CT Chest: No new lesions</li>
                      </ul>
                    </div>
                  </div>
                  <div className="flex items-center justify-between border-t border-slate-200 p-2">
                    <div className="text-[8px] text-slate-500">
                      Source: {openReport.source}
                    </div>
                    <div className="flex items-center gap-2">
                      <button className="rounded-md border border-slate-300 px-2 py-1 text-[8px] hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                        Copy with citations
                      </button>
                      <button className="rounded-md border border-slate-300 px-2 py-1 text-[8px] hover:bg-slate-50 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500">
                        Add to slides
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {openImage && (
            <div
              role="dialog"
              aria-modal={true}
              aria-labelledby="image-modal-title"
              ref={imageModalRef}
              onKeyDown={(e) => handleTrapKey(e, imageModalRef)}
              className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
            >
              <div className="w-[560px] max-w-[90vw] rounded-xl bg-white shadow-2xl ring-1 ring-slate-200">
                <div className="flex items-center justify-between border-b border-slate-200 px-4 py-2">
                  <h3
                    id="image-modal-title"
                    className="text-sm font-semibold text-slate-900"
                  >
                    {openImage.modality} · {openImage.date}
                  </h3>
                  <button
                    className="text-slate-600 hover:text-slate-900 focus:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500"
                    onClick={() => setOpenImage(null)}
                    aria-label="Close"
                  >
                    ✕
                  </button>
                </div>
                <div className="space-y-2 p-4 text-sm text-slate-800">
                  <div className="rounded-md bg-slate-50 p-3 ring-1 ring-slate-200">
                    {openImage.impression}
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    {openImage.frames.map((f: any, i: any) => (
                      <div
                        key={i}
                        className="flex h-28 items-center justify-center rounded-md bg-slate-100 text-[8px] text-slate-500 ring-1 ring-slate-200"
                      >
                        {f}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </motion.div>
    );
  }
);

BoardSummaryView.displayName = "BoardSummaryView";

export default BoardSummaryView;
