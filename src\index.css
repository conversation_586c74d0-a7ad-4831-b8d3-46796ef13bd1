@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --text-xs: 0.625rem;
    --text-sm: 0.75rem;
    --text-base: 0.875rem;
    --text-lg: 1rem;
    --text-xl: 1.125rem;
    --text-2xl: 1.25rem;
    --text-3xl: 1.5rem;
    --text-4xl: 1.875rem;
    --text-5xl: 2.25rem;
    --text-6xl: 2.75rem;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 3.5rem;
    --space-20: 4rem;
    --space-24: 4.5rem;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    --brand-primary: 220 85% 58%;
    --brand-primary-light: 220 85% 68%;
    --brand-primary-lighter: 220 85% 85%;
    --brand-primary-dark: 220 85% 48%;
    --brand-primary-darker: 220 85% 35%;
    --brand-primary-foreground: 0 0% 100%;
    --brand-secondary: 45 90% 60%;
    --brand-secondary-light: 45 90% 70%;
    --brand-secondary-dark: 45 90% 50%;
    --brand-secondary-foreground: 0 0% 15%;
    --text-primary-contrast: 220 20% 8%;
    --text-secondary-contrast: 220 15% 25%;
    --text-muted-contrast: 220 10% 42%;
    --focus-ring: 220 100% 60%;
    --error-indication: 0 100% 35%;
    --success-indication: 140 100% 25%;
    --background: 0 0% 100%;
    --foreground: 220 20% 8%;
    --card: 0 0% 100%;
    --card-foreground: 220 20% 8%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 20% 8%;
    --primary: 220 85% 58%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 220 85% 68%;
    --primary-lighter: 220 85% 85%;
    --primary-dark: 220 85% 48%;
    --secondary: 220 15% 95%;
    --secondary-foreground: 220 20% 8%;
    --secondary-accent: 45 90% 60%;
    --muted: 220 15% 95%;
    --muted-foreground: 220 10% 42%;
    --accent: 260 60% 50%;
    --accent-foreground: 0 0% 100%;
    --accent-light: 260 60% 95%;
    --accent-secondary: 45 85% 55%;
    --success: 140 65% 42%;
    --success-foreground: 0 0% 100%;
    --success-light: 140 65% 95%;
    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 10% 90%;
    --input: 220 15% 95%;
    --ring: 220 85% 58%;
    --gradient-medical: linear-gradient(
      135deg,
      hsl(210 90% 55%) 0%,
      hsl(140 65% 42%) 100%
    );
    --gradient-trust: linear-gradient(
      135deg,
      hsl(220 85% 50%) 0%,
      hsl(140 70% 40%) 100%
    );
    --gradient-hero-medical: linear-gradient(
      135deg,
      hsl(220 85% 58%) 0%,
      hsl(210 90% 55%) 50%,
      hsl(140 65% 42%) 100%
    );
    --gradient-purple: linear-gradient(
      135deg,
      #e6e6fa 0%,
      #dda0dd 50%,
      #da70d6 100%
    );
    --gradient-orange: linear-gradient(
      135deg,
      #fff8dc 0%,
      #ffe4b5 30%,
      #ffa500 70%,
      #ff8c00 100%
    );
    --gradient-blue: linear-gradient(
      135deg,
      #f0f8ff 0%,
      #b0e0e6 40%,
      #87ceeb 100%
    );
    --gradient-green: linear-gradient(
      135deg,
      #f0fff0 0%,
      #98fb98 50%,
      #90ee90 100%
    );
    --gradient-hero: linear-gradient(
      135deg,
      hsl(220 85% 58%) 0%,
      hsl(260 60% 50%) 100%
    );
    --gradient-soft: radial-gradient(
      ellipse at center,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(248, 248, 248, 0.8) 100%
    );
    --shadow-soft: 0 4px 20px rgba(34, 60, 120, 0.08);
    --shadow-medium: 0 8px 40px rgba(34, 60, 120, 0.12);
    --shadow-organic: 0 20px 60px rgba(34, 60, 120, 0.15);
    --transition-organic: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --radius: 0.75rem;
    --sidebar-background: 220 15% 98%;
    --sidebar-foreground: 220 20% 8%;
    --sidebar-primary: 220 85% 58%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 15% 95%;
    --sidebar-accent-foreground: 220 20% 8%;
    --sidebar-border: 220 10% 90%;
    --sidebar-ring: 220 85% 58%;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      "Inter",
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      sans-serif;
    font-feature-settings:
      "kern" 1,
      "liga" 1,
      "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: hsl(var(--brand-primary-primary));
}
::-webkit-scrollbar-thumb {
  background: linear-gradient(
    90deg,
    var(--tw-ring-color, #06b6d4),
    rgba(6, 182, 212, 0.65)
  );
  border-radius: 6px;
}
::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--brand-primary));
}
html {
  scroll-behavior: smooth;
}
@layer components {
  .text-display-hero {
    @apply text-display-lg font-light tracking-tight text-foreground md:text-display-lg;
    font-variation-settings: "wght" 300;
  }
  .text-display-large {
    @apply text-display-md font-light tracking-tight text-foreground md:text-display-md;
    font-variation-settings: "wght" 300;
  }
  .text-display-small {
    @apply text-display-sm font-light tracking-tight text-foreground md:text-display-sm;
    font-variation-settings: "wght" 300;
    font-variation-settings: "wght" 300;
  }
  .text-heading-major {
    @apply text-heading-1 font-normal tracking-tight text-foreground md:text-heading-1;
    font-variation-settings: "wght" 400;
  }
  .text-heading-section {
    @apply text-heading-2 font-normal tracking-tight text-foreground md:text-heading-2;
    font-variation-settings: "wght" 400;
  }
  .text-heading-subsection {
    @apply text-heading-3 font-medium tracking-tight text-foreground md:text-heading-3;
    font-variation-settings: "wght" 500;
  }
  .text-heading-card {
    @apply text-heading-4 font-medium tracking-tight text-foreground md:text-heading-4;
    font-variation-settings: "wght" 500;
  }
  .text-heading-minor {
    @apply text-heading-5 font-medium tracking-tight text-foreground md:text-heading-5;
    font-variation-settings: "wght" 500;
  }
  .text-heading-small {
    @apply text-heading-6 font-semibold tracking-tight text-foreground md:text-heading-6;
    font-variation-settings: "wght" 600;
  }
  .text-body-lead {
    @apply text-body-lg font-normal leading-relaxed text-foreground md:text-body-lg;
    font-variation-settings: "wght" 400;
  }
  .text-body-large {
    @apply text-body-md font-normal leading-relaxed text-foreground md:text-body-md;
    font-variation-settings: "wght" 400;
  }
  .text-body-default {
    @apply text-body-sm font-normal leading-relaxed text-foreground md:text-body-sm;
    font-variation-settings: "wght" 400;
  }
  .text-body-small {
    @apply text-body-xs font-normal leading-relaxed text-muted-foreground md:text-body-xs;
    font-variation-settings: "wght" 400;
  }
  img,
  video,
  iframe,
  canvas,
  svg,
  object {
    max-width: 100%;
    height: auto;
    display: block;
  }
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }
  body {
    overflow-x: hidden;
  }
  *:focus-visible {
    outline: 2px solid hsl(var(--focus-ring));
    outline-offset: 2px;
    border-radius: 4px;
  }
  @media (max-width: 600px) {
    html {
      font-size: 100%;
    }
  }
  @media (min-width: 601px) and (max-width: 1024px) {
    html {
      font-size: 100%;
    }
  }
  @media (min-width: 1025px) {
    html {
      font-size: 100%;
    }
  }
  @media (max-width: 640px) {
    .text-display-hero {
      @apply text-heading-2;
    }
    .text-display-large {
      @apply text-heading-3;
    }
    .text-heading-major {
      @apply text-heading-4;
    }
    .text-heading-section {
      @apply text-heading-5;
    }
  }
  @media (min-width: 1280px) {
    .text-display-hero {
      font-size: 3.5rem;
      line-height: 4rem;
    }
    .text-display-large {
      font-size: 2.75rem;
      line-height: 3.25rem;
    }
  }
}
@layer utilities {
  .text-display-hero {
    font-size: clamp(1.75rem, 5vw, 3.5rem);
    line-height: var(--leading-tight);
    font-weight: 600;
    letter-spacing: -0.02em;
  }
  .text-display-large {
    font-size: clamp(1.25rem, 2.5vw, 2rem);
    line-height: var(--leading-tight);
    font-weight: 300;
    letter-spacing: -0.01em;
  }
  .text-heading-1 {
    font-size: clamp(1.125rem, 2.25vw, 1.75rem);
    line-height: var(--leading-snug);
    font-weight: 600;
  }
  .text-heading-2 {
    font-size: clamp(1rem, 1.75vw, 1.375rem);
    line-height: var(--leading-snug);
    font-weight: 600;
  }
  .text-heading-3 {
    font-size: clamp(0.875rem, 1.25vw, 1.125rem);
    line-height: var(--leading-snug);
    font-weight: 600;
  }
  .text-body-large {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
    font-weight: 400;
  }
  .text-body-base {
    font-size: var(--text-base);
    line-height: var(--leading-normal);
    font-weight: 400;
  }
  .text-body-small {
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
    font-weight: 400;
  }
}
