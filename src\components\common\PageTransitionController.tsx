import { staggerContainer } from "@/lib/animations";
import { motion } from "framer-motion";
import React from "react";

interface Props {
  children: React.ReactNode;
}

const PageTransitionController: React.FC<Props> = ({ children }) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5, ease: "easeInOut" }}
    >
      <motion.div
        variants={staggerContainer}
        initial="hidden"
        animate="visible"
      >
        {children}
      </motion.div>
    </motion.div>
  );
};

export default PageTransitionController;
