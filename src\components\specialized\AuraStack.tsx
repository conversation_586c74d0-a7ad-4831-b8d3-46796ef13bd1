"use client";

import {
  AnimatePresence,
  motion,
  useScroll,
  useTransform,
} from "framer-motion";
import React, { memo, useEffect, useMemo, useRef, useState } from "react";

const plateSpacing = 20;
const STAGES: Array<{ title: string; copy: string; id: string }> = [
  {
    id: "data",
    title: "DATA",
    copy: "Aggregate EMR, LIS, and PACS data into a unified pipeline.",
  },
  {
    id: "context",
    title: "CONTEXT",
    copy: "Normalize to FHIR, map entities, and establish provenance.",
  },
  {
    id: "insight",
    title: "INSIGHT",
    copy: "Summarize longitudinal signals into actionable insights.",
  },
  {
    id: "coaching",
    title: "COACHING",
    copy: "Surface quick wins and cohort-based guidance for clinicians.",
  },
  {
    id: "orchestration",
    title: "ORCHESTRATION",
    copy: "Automate follow-ups and task routing across the care team.",
  },
  {
    id: "aura-guardian",
    title: "AURA GUARDIAN",
    copy: "Always-on safety checks and guidance—human-in-the-loop by design.",
  },
];

const AuraStack: React.FC = memo(() => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [currentStage, setCurrentStage] = useState<number>(0);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end end"],
  });

  const stageIndex = useTransform(scrollYProgress, (v) =>
    Math.min(6, Math.floor(v * 7))
  );

  useEffect(() => {
    return stageIndex.on("change", (latest) => {
      setCurrentStage(latest);
    });
  }, [stageIndex]);

  const visualLayers = useMemo(
    () => [
      { id: "base", path: "/aura-stack/00-base.png" },
      { id: "data", path: "/aura-stack/01-data.png" },
      { id: "context", path: "/aura-stack/02-context.png" },
      { id: "insight", path: "/aura-stack/03-insight.png" },
      { id: "coaching", path: "/aura-stack/04-coaching.png" },
      { id: "orchestration", path: "/aura-stack/05-orchestration.png" },
      { id: "aura", path: "/aura-stack/06-aura.png" },
      { id: "cap", path: "/aura-stack/07-cap.png" },
    ],
    []
  );

  return (
    <section
      id="aura-stack"
      ref={containerRef}
      className="relative w-full"
      style={{ height: `${STAGES.length * 100}vh` }}
    >
      <div className="sticky top-0 flex h-screen w-full flex-col overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <picture>
            <img
              src="/aura/aura-bg-desktop.png"
              alt=""
              className="h-full w-full object-cover"
            />
          </picture>
        </div>
        <div className="container mx-auto flex h-full items-center px-4 lg:px-8">
          <div className="grid w-full grid-cols-1 items-center gap-8 lg:grid-cols-2">
            <div className="relative z-10 text-center lg:text-left">
              <h2 className="text-display-hero font-light leading-tight text-stone-900">
                The Aura
                <br />
                <em className="font-bold italic text-primary">Stack.</em>
              </h2>
              <p className="text-body-large mx-auto mt-4 max-w-md font-light leading-relaxed text-stone-700/90 lg:mx-0">
                Orchestrating oncology care with AI. From scattered signals to
                steady care—one intelligent system.
              </p>
            </div>

            <div className="relative flex h-screen items-center justify-center">
              <div
                className="relative top-1/3 h-full w-full"
                style={{ transformStyle: "preserve-3d", perspective: "2500px" }}
              >
                {visualLayers.map((layer, index) => {
                  if (index === 0) {
                    const baseStart = 6 / 8;
                    const baseEnd = 7 / 8;
                    const opacity = useTransform(
                      scrollYProgress,
                      [baseStart, baseEnd],
                      [0, 1]
                    );
                    const y = useTransform(
                      scrollYProgress,
                      [baseStart, baseEnd, 7.5 / 8, 1],
                      [500, plateSpacing, plateSpacing, plateSpacing]
                    );
                    const scale = useTransform(
                      scrollYProgress,
                      [baseStart, baseEnd],
                      [0.9, 1]
                    );
                    return (
                      <motion.div
                        key={layer.id}
                        className="absolute inset-0"
                        style={{
                          opacity,
                          scale,
                          y,
                          transformStyle: "preserve-3d",
                          transformOrigin: "bottom center",
                        }}
                      >
                        <img
                          src={layer.path}
                          alt={layer.id}
                          className="h-[65vh] w-[65vh] object-contain"
                          style={{
                            filter: `drop-shadow(0 10px 20px rgba(0,0,0,0.1))`,
                          }}
                        />
                      </motion.div>
                    );
                  }
                  if (index === 7) {
                    const capStart = 6 / 8;
                    const capEnd = 7 / 8;
                    const opacity = useTransform(
                      scrollYProgress,
                      [capStart, capEnd],
                      [0, 1]
                    );
                    const y = useTransform(
                      scrollYProgress,
                      [capStart, capEnd, 7.5 / 8, 1],
                      [
                        -500,
                        -(6 * plateSpacing),
                        -(6 * plateSpacing + plateSpacing),
                        -(6 * plateSpacing + plateSpacing),
                      ]
                    );
                    const scale = useTransform(
                      scrollYProgress,
                      [capStart, capEnd],
                      [0.9, 1]
                    );
                    return (
                      <motion.div
                        key={layer.id}
                        className="absolute inset-0"
                        style={{
                          opacity,
                          scale,
                          y,
                          transformStyle: "preserve-3d",
                          transformOrigin: "bottom center",
                        }}
                      >
                        <img
                          src={layer.path}
                          alt={layer.id}
                          className="h-[65vh] w-[65vh] object-contain"
                          style={{
                            filter: `drop-shadow(0 ${10 + index * 2}px ${20 + index * 3}px rgba(0,0,0,${0.1 + index * 0.02}))`,
                          }}
                        />
                      </motion.div>
                    );
                  }
                  const stageStart = index === 1 ? 0 : (index - 1) / 8;
                  const stageEnd = index / 8;
                  const stageMid = stageEnd + 0.02;
                  const stageSettle = stageMid + 0.02;

                  const finalY = -(index * plateSpacing);
                  const tightenedY = finalY + 2;

                  const opacity = useTransform(
                    scrollYProgress,
                    [stageStart, stageEnd],
                    [index === 1 ? 1 : 0, 1]
                  );
                  const scale = useTransform(
                    scrollYProgress,
                    [stageStart, stageEnd],
                    [1, 1]
                  );
                  const y = useTransform(
                    scrollYProgress,
                    index === 1
                      ? [0, 7.5 / 8, 1]
                      : [
                          stageStart,
                          stageEnd,
                          stageMid,
                          stageSettle,
                          7.5 / 8,
                          1,
                        ],
                    index === 1
                      ? [finalY, tightenedY, tightenedY]
                      : [
                          350 + index * 20,
                          finalY,
                          finalY - 3,
                          finalY,
                          tightenedY,
                          tightenedY,
                        ]
                  );

                  return (
                    <motion.div
                      key={layer.id}
                      className="absolute inset-0"
                      style={{
                        opacity,
                        scale,
                        y,
                        transformStyle: "preserve-3d",
                        transformOrigin: "bottom center",
                      }}
                    >
                      <img
                        src={layer.path}
                        alt={layer.id}
                        className="h-[65vh] w-[65vh] object-contain"
                        style={{
                          filter: `drop-shadow(0 ${10 + index * 2}px ${20 + index * 3}px rgba(0,0,0,${0.1 + index * 0.02}))`,
                        }}
                      />
                    </motion.div>
                  );
                })}
              </div>

              <div className="absolute right-0 z-20 hidden items-center lg:flex">
                <AnimatePresence mode="wait">
                  {currentStage < 6 && (
                    <>
                      <div className="relative h-8 w-8">
                        <div className="absolute left-0 top-1/2 h-2 w-2 -translate-y-1/3 rounded-full bg-gray-900" />
                        <div className="absolute left-0 top-1/2 h-0.5 w-8 bg-gray-900" />
                      </div>
                      <motion.div
                        key={currentStage}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -20 }}
                        transition={{ duration: 0.3 }}
                        className="w-40 rounded-lg bg-white/90 p-2 shadow-lg backdrop-blur-sm"
                        style={{ transformOrigin: "left center" }}
                      >
                        <h3 className="mb-2 text-[16px] font-bold text-stone-900">
                          {STAGES[currentStage]?.title}
                        </h3>
                        <p className="text-[14px] font-light leading-relaxed text-stone-700/90">
                          {STAGES[currentStage]?.copy}
                        </p>
                      </motion.div>
                    </>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
});

AuraStack.displayName = "AuraStack";

export default AuraStack;
