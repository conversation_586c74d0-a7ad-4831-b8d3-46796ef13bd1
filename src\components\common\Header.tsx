import { <PERSON>Button } from "@/components/animations";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Sheet<PERSON>rigger } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";

interface NavLink {
  href: string;
  label: string;
  external?: boolean;
}

const Header: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    let lastScrollY = window.scrollY;
    const handleScroll = () => {
      const currentY = window.scrollY;
      lastScrollY = currentY;
    };
    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navLinks: NavLink[] = [
    { href: "#aura-stack", label: "Approach" },
    { href: "#aura-branches", label: "Product" },
    { href: "#scrollytelling", label: "Advantage" },
    {
      href: import.meta.env.VITE_SURVEY_FORM || "",
      label: "Contribute",
      external: true,
    },
  ];

  const handleNavClick = useCallback((href: string): void => {
    setIsOpen(false);
    if (href.startsWith("#")) {
      const element = document.querySelector(href);
      if (element) {
        const top = element.getBoundingClientRect().top + window.scrollY;
        window.scrollTo({ top, behavior: "smooth" });
      }
    }
  }, []);

  const handlePilotOpen = useCallback((): void => {
    window.dispatchEvent(new Event("early-pilot-open"));
  }, []);

  return (
    <header className="fixed inset-x-0 top-3 z-50 flex justify-center">
      <nav className="flex w-full max-w-3xl items-center justify-between rounded-full border border-white/30 bg-white/30 px-5 py-2 shadow-2xl backdrop-blur-2xl backdrop-saturate-150 transition-all duration-500 ease-in-out">
        <a
          href="#hero"
          aria-label="Go to homepage"
          className="flex items-center gap-2 transition-opacity hover:opacity-80"
          onClick={(e) => {
            e.preventDefault();
            handleNavClick("#hero");
          }}
        >
          <img src="/logo.png" alt="Entheory Logo" className="h-7 w-auto" />
        </a>

        <div className="hidden items-center space-x-6 md:flex">
          {navLinks.map((link) => (
            <a
              key={link.href}
              href={link.href}
              className="text-sm font-medium text-black transition-colors hover:text-foreground"
              onClick={(e) => {
                if (link.external) return;
                e.preventDefault();
                handleNavClick(link.href);
              }}
              {...(link.external && {
                target: "_blank",
                rel: "noopener noreferrer",
              })}
            >
              {link.label}
            </a>
          ))}
        </div>

        <div className="flex items-center gap-2">
          <InteractiveButton
            variant="default"
            size="sm"
            className="hidden md:inline-flex"
            onClick={handlePilotOpen}
          >
            Early Pilot
          </InteractiveButton>

          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <InteractiveButton
                variant="ghost"
                className="md:hidden"
                aria-label="Open menu"
              >
                <Menu className="h-5 w-5" />
              </InteractiveButton>
            </SheetTrigger>
            <SheetContent side="right" className="w-[280px] p-0">
              <div className="flex h-full flex-col">
                <div className="flex items-center justify-between border-b px-4 py-6">
                  <span className="text-[22px] font-bold">Entheory</span>
                </div>
                <div className="flex flex-col gap-1 p-4">
                  {navLinks.map((link) => (
                    <a
                      key={link.href}
                      href={link.href}
                      className="rounded-md px-3 py-2 text-base font-medium text-foreground transition-colors hover:bg-muted"
                      onClick={(e) => {
                        if (link.external) return;
                        e.preventDefault();
                        handleNavClick(link.href);
                      }}
                      {...(link.external && {
                        target: "_blank",
                        rel: "noopener noreferrer",
                      })}
                    >
                      {link.label}
                    </a>
                  ))}
                  <InteractiveButton
                    variant="primary"
                    size="lg"
                    className="mt-4"
                    onClick={handlePilotOpen}
                  >
                    Early Pilot Access
                  </InteractiveButton>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </nav>
    </header>
  );
};

export default Header;
