"use client";

import { motion } from "framer-motion";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";

type TimelineItem = {
  id: string;
  date: string;
  title: string;
  source: "EMR" | "LIS" | "PACS";
  facility: string;
  specimenSite?: string;
};
const historyItems: TimelineItem[] = [
  {
    id: "h2",
    date: "2025-08-24",
    title: "Oncology Consult — Dr. ABC",
    source: "EMR",
    facility: "LMN",
  },
  {
    id: "h1",
    date: "2025-08-12",
    title: "Discharge Summary — MRN AP-2025-847",
    source: "EMR",
    facility: "XYZ",
  },
];
const labItems: TimelineItem[] = [
  {
    id: "l2",
    date: "2025-08-20",
    title: "Pathology: Breast Core Biopsy — ER+/PR+, HER2–",
    source: "LIS",
    facility: "ABC",
    specimenSite: "Left breast core",
  },
  {
    id: "l1",
    date: "2025-08-18",
    title: "CBC w/ diff — Hgb 10.4 (L), ANC 1.2 (L)",
    source: "LIS",
    facility: "ABC",
  },
];
const imagingItems: TimelineItem[] = [
  {
    id: "i2",
    date: "2025-08-22",
    title: "CT Thorax/Abdomen — No distant mets; Ax LN 12mm",
    source: "PACS",
    facility: "PQR",
  },
  {
    id: "i1",
    date: "2025-08-10",
    title: "Mammogram — Suspicious calcifications BIRADS 4b",
    source: "PACS",
    facility: "XYZ",
  },
];

export const TimelineView: React.FC = memo(() => {
  type Range = "30" | "90" | "180" | "since";

  const [query, setQuery] = useState("");
  const [range, setRange] = useState<Range>("90");
  const [density, setDensity] = useState<"comfortable" | "compact">(
    "comfortable"
  );
  const [openItem, setOpenItem] = useState<null | {
    row: "history" | "labs" | "imaging";
    id: string;
  }>(null);
  const [sheetTab, setSheetTab] = useState<"summary" | "full" | "prov">(
    "summary"
  );
  const [lastSyncedSec, setLastSyncedSec] = useState(18);
  const [pulseUntil, setPulseUntil] = useState<number>(0);

  const options: { k: Range; l: string }[] = [
    { k: "30", l: "30 days" },
    { k: "90", l: "90 days" },
    { k: "180", l: "180 days" },
    { k: "since", l: "Since beginning" },
  ];

  const handleRangeChange = useCallback(
    (newRange: "30" | "90" | "180" | "since") => {
      setRange(newRange);
    },
    []
  );

  const handleSyncClick = useCallback(() => {
    setLastSyncedSec(0);
    setPulseUntil(Date.now() + 6000);
  }, []);

  useEffect(() => {
    const id = setInterval(() => {
      setLastSyncedSec((s) => s + 1);
    }, 1000);
    return () => clearInterval(id);
  }, []);

  const Section: React.FC<{
    label: string;
    items: TimelineItem[];
    row: "history" | "labs" | "imaging";
  }> = memo(({ label, items, row }) => {
    const sortedItems = useMemo(
      () => [...items].sort((a, b) => b.date.localeCompare(a.date)),
      [items]
    );
    const handleItemOpen = useCallback(
      (id: string) => {
        setOpenItem({ row, id });
      },
      [row]
    );

    return (
      <div className="rounded-lg bg-white ring-1 ring-slate-200/80">
        <div className="flex items-center justify-between rounded-t-lg bg-slate-50/80 px-4 py-1">
          <div className="text-[10px] font-semibold uppercase tracking-wider text-slate-700">
            {label}
          </div>
          <div className="text-[10px] text-slate-500">
            {items.length} items • Updated {lastSyncedSec}s ago
          </div>
        </div>
        <ul className="divide-y divide-slate-100">
          {sortedItems.map((it) => (
            <li
              key={it.id}
              className={`px-4 ${
                density === "compact" ? "py-2" : "py-3"
              } flex items-center gap-2`}
            >
              <div className="w-24 shrink-0 text-[10px] text-slate-500">
                {it.date}
              </div>
              <div className="min-w-0 flex-1">
                <p className="truncate text-[10px] text-slate-800">
                  {it.title}
                </p>
              </div>
              <div className="mt-1 flex flex-wrap items-center gap-1.5">
                <span className="rounded-full border border-slate-200 bg-slate-50 px-2 py-0.5 text-[11px] font-medium text-slate-600">
                  {it.source} — {it.facility}
                </span>
                {it.specimenSite && (
                  <span className="rounded-full border border-slate-200 bg-slate-50 px-2 py-0.5 text-[11px] font-medium text-slate-600">
                    Specimen: {it.specimenSite}
                  </span>
                )}
              </div>
              <button
                className="ml-auto rounded-md border border-slate-300 bg-white px-3 py-1 text-[10px] font-medium text-slate-700 hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-emerald-500"
                onClick={() => handleItemOpen(it.id)}
                aria-label={`Open ${it.title}`}
              >
                Open
              </button>
            </li>
          ))}
        </ul>
      </div>
    );
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.4, ease: [0.22, 1, 0.36, 1] }}
      className="flex h-full flex-col items-center justify-center gap-6 px-4 pb-4 pt-20 md:flex-row"
    >
      <div className="flex w-full max-w-md flex-col items-start text-left md:order-first md:w-1/4">
        <div className="inline-flex items-center gap-2 rounded-[20px] border border-white/70 bg-white/90 px-5 py-2.5 shadow-[0_8px_20px_rgba(0,0,0,0.1)] backdrop-blur-2xl">
          <div
            className="h-1.5 w-1.5 rounded-full"
            style={{ backgroundColor: "#10b981" }}
          />
          <div className="flex items-baseline gap-1">
            <span className="text-[10px] font-bold tracking-wider text-slate-800">
              ONE TIMELINE
            </span>
            <span className="text-[8px] font-medium tracking-wide text-slate-600">
              ONE PATIENT
            </span>
          </div>
        </div>
        <h2 className="mt-6 text-6xl font-light leading-[1.05] text-white">
          <span className="font-semibold">One Timeline,</span>
          <br />
          <span className="italic">One Patient</span>
        </h2>
        <div className="mt-6 max-w-md">
          <p className="text-[14px] font-light text-white/90">
            Longitudinal view with provenance — history, labs & imaging stitched
            into context.
          </p>
          <div
            role="list"
            aria-label="Capabilities"
            className="mt-4 flex flex-wrap gap-2"
          >
            {["Source badges", "Provenance links", "Real‑time sync"].map(
              (cap) => (
                <span
                  key={cap}
                  role="listitem"
                  className="inline-flex items-center gap-1 rounded-full border border-white/60 bg-white/90 px-3 py-1 text-[10px] font-medium text-slate-800 backdrop-blur-md"
                >
                  {cap}
                </span>
              )
            )}
          </div>
        </div>
      </div>

      <div className="relative w-full flex-1 md:order-last md:w-2/3">
        <div className="relative min-h-0 w-full flex-1 overflow-hidden rounded-xl bg-white/95 shadow-2xl ring-1 ring-slate-200/60 backdrop-blur-sm">
          {/* App Header */}
          <div className="border-b border-slate-200 bg-white/80 px-4 py-2 backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <img src="/logo.png" alt="Entheory" className="h-6 w-auto" />
                <nav className="hidden items-center gap-6 sm:flex">
                  <button className="text-[10px] text-gray-600 hover:text-gray-900">
                    Records
                  </button>
                  <button className="text-[10px] font-medium text-emerald-600">
                    Timeline
                  </button>
                  <button className="text-[10px] text-gray-600 hover:text-gray-900">
                    Summary
                  </button>
                  <button className="text-[10px] text-gray-600 hover:text-gray-900">
                    Actions
                  </button>
                </nav>
              </div>
              <button className="rounded-md bg-emerald-500 px-4 py-1.5 text-[10px] font-medium text-white hover:bg-emerald-600">
                Export ▾
              </button>
            </div>
          </div>
          {/* Filters Header */}
          <div className="border-b border-slate-200 bg-white/70 px-4 py-2 backdrop-blur-sm">
            <div className="flex flex-wrap items-center justify-between gap-x-4 gap-y-2">
              <input
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search timeline..."
                className="w-full rounded-md border border-gray-300 bg-gray-50 px-3 py-1 text-[10px] focus:outline-none focus:ring-2 focus:ring-emerald-500 sm:w-auto sm:flex-1"
              />
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex items-center gap-2">
                  <label className="text-[10px] text-slate-600">Time</label>
                  <div className="inline-flex overflow-hidden rounded-full border border-gray-300">
                    {options.map((opt) => (
                      <button
                        key={opt.k}
                        role="tab"
                        aria-selected={range === opt.k}
                        className={`px-2 py-0.5 text-[10px] font-medium ${
                          range === opt.k
                            ? "bg-gray-100 text-gray-900"
                            : "bg-white text-gray-600"
                        }`}
                        onClick={() => handleRangeChange(opt.k)}
                      >
                        {opt.l}
                      </button>
                    ))}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-[10px] text-slate-600">Density</label>
                  <div className="inline-flex overflow-hidden rounded-full border border-gray-300">
                    <button
                      className={`px-2 py-0.5 text-[10px] font-medium ${
                        density === "comfortable"
                          ? "bg-gray-100 text-gray-900"
                          : "bg-white text-gray-600"
                      }`}
                      onClick={() => setDensity("comfortable")}
                    >
                      Std
                    </button>
                    <button
                      className={`px-2 py-0.5 text-[10px] font-medium ${
                        density === "compact"
                          ? "bg-gray-100 text-gray-900"
                          : "bg-white text-gray-600"
                      }`}
                      onClick={() => setDensity("compact")}
                    >
                      Compact
                    </button>
                  </div>
                </div>
                <button
                  className="relative flex items-center rounded-md p-1"
                  onClick={handleSyncClick}
                  aria-label={`Last synced ${lastSyncedSec}s ago`}
                >
                  <span className="relative flex h-2 w-2">
                    <span
                      className={`${
                        Date.now() < pulseUntil ? "animate-ping" : ""
                      } absolute inline-flex h-full w-full rounded-full bg-emerald-400 opacity-75`}
                    ></span>
                    <span className="relative inline-flex h-2 w-2 rounded-full bg-emerald-500"></span>
                  </span>
                  <span className="ml-1.5 text-[10px] text-slate-600">
                    Synced {lastSyncedSec}s ago
                  </span>
                </button>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex h-full" style={{ maxHeight: "80vh" }}>
            <div className="space-y-2 p-2">
              <Section label="History" items={historyItems} row="history" />
              <Section label="Labs" items={labItems} row="labs" />
              <Section label="Imaging" items={imagingItems} row="imaging" />
            </div>
          </div>

          {/* Side Sheet Dialog */}
          <div
            role="dialog"
            aria-modal={!!openItem}
            aria-labelledby="sheet-title"
            className={`absolute right-0 top-0 h-full w-[450px] max-w-[80vw] transform-gpu border-l border-gray-200 bg-white shadow-2xl transition-transform duration-300 ease-in-out ${
              openItem ? "translate-x-0" : "translate-x-full"
            }`}
          >
            {openItem && (
              <>
                <div className="flex items-center justify-between border-b border-gray-200 px-5 py-3">
                  <div className="flex items-center gap-3">
                    <div className="flex h-7 w-7 items-center justify-center rounded-md bg-emerald-100 text-[10px] font-bold text-emerald-700">
                      {openItem.row === "labs"
                        ? "LIS"
                        : openItem.row === "imaging"
                          ? "PACS"
                          : "EMR"}
                    </div>
                    <h3
                      id="sheet-title"
                      className="text-base font-semibold text-slate-900"
                    >
                      Report & Provenance
                    </h3>
                  </div>
                  <button
                    className="text-slate-500 hover:text-slate-800"
                    onClick={() => setOpenItem(null)}
                    aria-label="Close"
                  >
                    ✕
                  </button>
                </div>
                <div className="flex items-center gap-2 border-b border-gray-200 px-5 py-2">
                  <button
                    className={`rounded-md border px-3 py-1 text-[10px] ${
                      sheetTab === "summary"
                        ? "border-emerald-300 bg-emerald-50 text-emerald-700"
                        : "border-gray-300 bg-white"
                    }`}
                    onClick={() => setSheetTab("summary")}
                  >
                    Summary
                  </button>
                  <button
                    className={`rounded-md border px-3 py-1 text-[10px] ${
                      sheetTab === "full"
                        ? "border-emerald-300 bg-emerald-50 text-emerald-700"
                        : "border-gray-300 bg-white"
                    }`}
                    onClick={() => setSheetTab("full")}
                  >
                    Full Report
                  </button>
                  <button
                    className={`rounded-md border px-3 py-1 text-[10px] ${
                      sheetTab === "prov"
                        ? "border-emerald-300 bg-emerald-50 text-emerald-700"
                        : "border-gray-300 bg-white"
                    }`}
                    onClick={() => setSheetTab("prov")}
                  >
                    Provenance
                  </button>
                </div>
                <div className="h-[calc(100%-150px)] space-y-4 overflow-auto p-5">
                  {sheetTab === "summary" && (
                    <div className="space-y-3">
                      <div className="rounded-lg border border-gray-200 p-3">
                        <p className="text-[10px] text-slate-600">Synopsis</p>
                        <p className="text-[10px] text-slate-900">
                          Key values summarized with trends where applicable.
                        </p>
                      </div>
                      <div className="rounded-lg border border-gray-200 p-3">
                        <p className="text-[10px] text-slate-600">Highlights</p>
                        <p className="text-[10px] text-slate-900">
                          ER+/PR+, HER2– · Hgb 10.4 (L) · ANC 1.2 (L)
                        </p>
                      </div>
                    </div>
                  )}
                  {sheetTab === "full" && (
                    <div className="space-y-3">
                      <div className="rounded-lg border border-gray-200 p-3">
                        <p className="text-[10px] text-slate-600">
                          Full report
                        </p>
                        <p className="text-[10px] text-slate-900">
                          PDF or structured view would render here.
                        </p>
                      </div>
                    </div>
                  )}
                  {sheetTab === "prov" && (
                    <div className="space-y-3">
                      <div className="rounded-lg border border-gray-200 p-3">
                        <p className="text-[10px] text-slate-600">Provenance</p>
                        <p className="text-[10px] text-slate-900">
                          DiagnosticReport → Observation[] · Specimen · Device ·
                          timestamps (collection, issued)
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between border-t border-gray-200 px-5 py-3">
                  <div className="flex items-center gap-2">
                    <button className="rounded-full border border-gray-300 px-2.5 py-1 text-[10px]">
                      Order follow‑up test
                    </button>
                    <button className="rounded-full border border-gray-300 px-2.5 py-1 text-[10px]">
                      Add to board pack
                    </button>
                  </div>
                  <div className="flex items-center gap-2">
                    <button className="rounded-full border border-gray-300 px-2.5 py-1 text-[10px]">
                      Flag inconsistency
                    </button>
                    <button className="rounded-full border border-emerald-300 bg-emerald-50 px-2.5 py-1 text-[10px] text-emerald-700">
                      Mark reviewed ✓
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
});

TimelineView.displayName = "TimelineView";
export default TimelineView;
