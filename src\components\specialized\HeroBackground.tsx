import React, { memo, useCallback, useState } from "react";

interface HeroBackgroundProps {
  className?: string;
  priority?: boolean;
  onLoad?: () => void;
  onError?: () => void;
}

const HeroBackground: React.FC<HeroBackgroundProps> = memo(
  ({ className = "", priority = true, onLoad, onError }) => {
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageError, setImageError] = useState(false);

    const handleImageLoad = useCallback(() => {
      setImageLoaded(true);
      onLoad?.();
    }, [onLoad]);

    const handleImageError = useCallback(() => {
      setImageError(true);
      onError?.();
    }, [onError]);

    return (
      <div
        className={`pointer-events-none absolute inset-0 z-0 overflow-hidden ${className}`}
      >
        <div
          className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-slate-100"
          aria-hidden="true"
        />
        <picture aria-hidden="true" className="absolute inset-0">
          <img
            src="/hero/hero-bg-desktop.png"
            alt="Entheory Logo"
            className={`h-full w-full object-cover transition-opacity duration-300 ${
              imageLoaded ? "opacity-100" : "opacity-0"
            }`}
            style={{
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }}
            loading={priority ? "eager" : "lazy"}
            decoding="async"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </picture>
        {!imageLoaded && !imageError && (
          <div
            className="absolute inset-0 flex items-center justify-center bg-slate-50/20"
            aria-hidden="true"
          >
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-slate-300 border-t-slate-600" />
          </div>
        )}
      </div>
    );
  }
);

HeroBackground.displayName = "HeroBackground";
export default HeroBackground;
