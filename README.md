# Entheory OncoConnect - Implementation Documentation

## 🎯 Project Overview

A premium, scroll-driven healthcare platform featuring nature-inspired animations and Microsoft AI-style transitions. Built with React, TypeScript, GSAP, and Framer Motion.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Development server
npm run dev

# Production build
npm run build

# Preview production build
npm run preview
```
