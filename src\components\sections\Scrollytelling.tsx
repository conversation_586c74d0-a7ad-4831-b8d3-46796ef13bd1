"use client";

import { InteractiveButton } from "@/components/animations";
import { motion, useScroll, useTransform } from "framer-motion";
import React, { memo, useEffect, useMemo, useRef, useState } from "react";

export interface ScrollytellingFrame {
  id: string;
  headline: React.ReactNode;
  description: string;
  image: string;
  alt: string;
  showCTA?: boolean;
}

const frames: ScrollytellingFrame[] = [
  {
    id: "frame-security",
    headline: (
      <>
        Privacy, safety <br />
        <em className="font-bold italic text-primary">and control</em> <br />
        <span className="font-normal not-italic text-gray-700">by design</span>
      </>
    ),
    description: "",
    image: "scrollytelling/security-shield.png",
    alt: "Security shield",
  },
  {
    id: "frame-1",
    headline: (
      <>
        Stop chasing. <br />
        <em className="font-bold italic text-primary">Start seeing.</em>
      </>
    ),
    description:
      "Auto-fetch with consent. OCR for PDFs. Units & dates harmonized (with source).",
    image: "scrollytelling/frame-a-rivers.png",
    alt: "Confluence of rivers representing data streams merging into one",
  },
  {
    id: "frame-2",
    headline: (
      <>
        Standards in. <br />
        <em className="font-bold italic text-primary">Standards out.</em>
      </>
    ),
    description:
      "FHIR bundles & DICOM links. ABDM consent artifacts preserved. Export with citations.",
    image: "scrollytelling/frame-b-mycelium.png",
    alt: "Mycelium threads connecting standards",
  },
  {
    id: "frame-3",
    headline: (
      <>
        Care, <br />
        <em className="font-bold italic text-primary">in motion.</em>
      </>
    ),
    description:
      "One-click orders, pre-filled. Trial matches & safety checks. Board pack in < 3 min.",
    image: "scrollytelling/frame-c-fireflies.png",
    alt: "Fireflies along a winding path",
  },
  {
    id: "frame-4",
    headline: (
      <>
        Where connection <br />
        <em className="font-bold italic text-primary">is infinite.</em>
      </>
    ),
    description: "The oncology OS for interoperable, efficient care.",
    image: "scrollytelling/frame-d-emblem.png",
    alt: "Hand holding Entheory emblem",
    showCTA: true,
  },
];

const handlePilotClick = () => {
  window.dispatchEvent(new Event("early-pilot-open"));
};

const Scrollytelling: React.FC = memo(() => {
  const sectionRef = useRef<HTMLElement>(null);
  const [imagesLoaded, setImagesLoaded] = useState(false);
  const imagePromises = useMemo(
    () =>
      frames.map(
        (frame) =>
          new Promise<void>((resolve, reject) => {
            const img = new Image();
            img.src = frame.image;
            img.onload = () => resolve();
            img.onerror = () => reject(`Failed to load ${frame.image}`);
          })
      ),
    []
  );

  useEffect(() => {
    Promise.all(imagePromises)
      .then(() => setImagesLoaded(true))
      .catch(() => {});
  }, [imagePromises]);

  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start start", "end end"],
  });

  return (
    <section
      id="scrollytelling"
      ref={sectionRef}
      className="relative w-full"
      style={{ height: `${frames.length * 100}vh` }}
    >
      <div className="sticky top-0 flex h-screen w-full items-center justify-center overflow-hidden">
        {!imagesLoaded && (
          <div className="absolute inset-0 z-20 flex items-center justify-center bg-white/50 backdrop-blur-sm">
            <p>Loading visualizations...</p>
          </div>
        )}
        {frames.map((frame, index) => {
          const start = index / frames.length;
          const end = (index + 1) / frames.length;
          const opacity =
            index === 0
              ? useTransform(scrollYProgress, [0, 0.05, 0.15], [1, 1, 0])
              : index === frames.length - 1
                ? useTransform(scrollYProgress, [start, start + 0.1], [0, 1])
                : useTransform(
                    scrollYProgress,
                    [start, start + 0.1, end - 0.1, end],
                    [0, 1, 1, 0]
                  );

          const scale =
            index === 0
              ? 1
              : useTransform(
                  scrollYProgress,
                  [start, start + 0.1, end - 0.1, end],
                  [0.95, 1, 1, 0.95]
                );

          return (
            <motion.article
              key={frame.id}
              className="absolute inset-0 flex h-full w-full flex-col items-center justify-center p-4 sm:p-6 lg:flex-row lg:p-8"
              style={{ opacity, scale }}
            >
              <div className="flex w-full flex-col justify-center text-center lg:w-1/2 lg:text-left">
                <h2 className="text-display-hero sm:text-display-large md:text-display-hero font-light leading-tight text-gray-900">
                  {frame.headline}
                </h2>
                {frame.description && (
                  <p className="text-body-large mt-4 max-w-lg text-sm text-gray-700 sm:text-base">
                    {frame.description}
                  </p>
                )}
                {frame.showCTA && (
                  <div className="mt-6">
                    <InteractiveButton
                      size="xl"
                      variant="default"
                      onClick={handlePilotClick}
                    >
                      Partner for an early pilot →
                    </InteractiveButton>
                  </div>
                )}
              </div>

              <div className="relative flex w-full items-center justify-center lg:mt-0 lg:w-1/2">
                <img
                  src={frame.image}
                  alt={frame.alt}
                  className="h-full max-h-screen w-full object-contain"
                  style={{
                    filter: "drop-shadow(0 20px 40px rgba(0,0,0,0.1))",
                  }}
                />
              </div>
            </motion.article>
          );
        })}
      </div>
    </section>
  );
});

Scrollytelling.displayName = "Scrollytelling";

export default Scrollytelling;
